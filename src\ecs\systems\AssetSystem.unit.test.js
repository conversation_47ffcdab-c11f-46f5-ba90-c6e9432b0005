// src/ecs/systems/AssetSystem.unit.test.js
// 资源系统单元测试

import { AssetSystem } from './AssetSystem.js';
import { AssetComponent } from '../components/AssetComponent.js';
import { Entity } from '../Entity.js';
import { ASSET_TYPES } from '../../assets/AssetTypes.js';

// Mock Babylon.js Scene
class MockScene {
    constructor() {
        this.meshes = [];
        this.textures = [];
        this.sounds = [];
    }
    
    dispose() {
        // Mock dispose
    }
}

// Mock AssetManager
class MockAssetManager {
    constructor() {
        this.isInitialized = false;
        this.loadedAssets = new Map();
        this.loadPromises = new Map();
    }
    
    async initialize() {
        this.isInitialized = true;
    }
    
    async loadAsset(url, type, options) {
        // 模拟异步加载
        await new Promise(resolve => setTimeout(resolve, 10));
        
        const asset = {
            url,
            type,
            data: `mock-${type}-data`,
            dispose: jest.fn()
        };
        
        this.loadedAssets.set(options.id || url, asset);
        return asset;
    }
    
    getAsset(id) {
        return this.loadedAssets.get(id) || null;
    }
    
    releaseAsset(id) {
        const asset = this.loadedAssets.get(id);
        if (asset) {
            this.loadedAssets.delete(id);
            return true;
        }
        return false;
    }
    
    getPerformanceStats() {
        return {
            totalLoaded: this.loadedAssets.size,
            totalFailed: 0,
            cacheHits: 0,
            cacheMisses: 0
        };
    }
    
    dispose() {
        this.loadedAssets.clear();
    }
    
    on() {}
    emit() {}
}

// Mock定时器
jest.useFakeTimers();

describe('AssetSystem', () => {
    let scene;
    let assetSystem;
    let mockAssetManager;
    
    beforeEach(() => {
        scene = new MockScene();
        assetSystem = new AssetSystem(scene, {
            autoLoadOnAdd: true,
            enableBatchLoading: false, // 禁用批处理以简化测试
            enableAutoCleanup: false   // 禁用自动清理以简化测试
        });
        
        // 替换资源管理器为mock
        mockAssetManager = new MockAssetManager();
        assetSystem.assetManager = mockAssetManager;
        assetSystem.assetManager.isInitialized = true;
    });
    
    afterEach(() => {
        if (assetSystem && !assetSystem.isDestroyed) {
            assetSystem.dispose();
        }
        jest.clearAllTimers();
    });
    
    describe('初始化', () => {
        test('应该正确创建资源系统', () => {
            expect(assetSystem).toBeDefined();
            expect(assetSystem.scene).toBe(scene);
            expect(assetSystem.requiredComponents).toContain(AssetComponent);
        });
        
        test('应该正确初始化', () => {
            assetSystem.initialize();
            
            expect(assetSystem.isInitialized).toBe(true);
        });
    });
    
    describe('实体管理', () => {
        let entity;
        let assetComponent;
        
        beforeEach(() => {
            assetSystem.initialize();
            
            entity = new Entity('test-entity');
            assetComponent = new AssetComponent(entity);
            entity.addComponent(assetComponent);
        });
        
        test('应该能够添加实体', () => {
            assetSystem.addEntity(entity);
            
            expect(assetSystem.entities.has(entity)).toBe(true);
            expect(assetComponent.assetManager).toBe(mockAssetManager);
            expect(assetSystem.stats.totalEntities).toBe(1);
        });
        
        test('应该能够移除实体', () => {
            assetSystem.addEntity(entity);
            assetSystem.removeEntity(entity);
            
            expect(assetSystem.entities.has(entity)).toBe(false);
            expect(assetSystem.stats.totalEntities).toBe(0);
        });
        
        test('添加实体时应该自动加载资源', async () => {
            // 添加资源配置
            assetComponent.addAsset('testTexture', 'texture.png', ASSET_TYPES.TEXTURE);
            
            const loadSpy = jest.spyOn(assetSystem, 'loadEntityAssets');
            
            assetSystem.addEntity(entity);
            
            // 等待自动加载延迟
            jest.advanceTimersByTime(200);
            
            expect(loadSpy).toHaveBeenCalledWith(entity);
        });
    });
    
    describe('资源加载', () => {
        let entity;
        let assetComponent;
        
        beforeEach(() => {
            assetSystem.initialize();
            
            entity = new Entity('test-entity');
            assetComponent = new AssetComponent(entity);
            entity.addComponent(assetComponent);
            
            assetSystem.addEntity(entity);
        });
        
        test('应该能够加载实体资源', async () => {
            // 添加资源配置
            assetComponent.addAsset('testTexture', 'texture.png', ASSET_TYPES.TEXTURE);
            assetComponent.addAsset('testSound', 'sound.mp3', ASSET_TYPES.SOUND);
            
            await assetSystem.loadEntityAssets(entity);
            
            expect(assetComponent.hasAsset('testTexture')).toBe(true);
            expect(assetComponent.hasAsset('testSound')).toBe(true);
            expect(assetSystem.stats.loadedEntities).toBe(1);
        });
        
        test('应该处理加载错误', async () => {
            // Mock加载失败
            mockAssetManager.loadAsset = jest.fn().mockRejectedValue(new Error('Load failed'));
            
            assetComponent.addAsset('failTexture', 'fail.png', ASSET_TYPES.TEXTURE);
            
            await assetSystem.loadEntityAssets(entity);
            
            expect(assetSystem.stats.failedEntities).toBe(1);
        });
        
        test('应该跳过已在加载的实体', async () => {
            assetComponent.addAsset('testTexture', 'texture.png', ASSET_TYPES.TEXTURE);
            assetComponent.isLoading = true;
            
            const loadSpy = jest.spyOn(assetComponent, 'loadAllAssets');
            
            await assetSystem.loadEntityAssets(entity);
            
            expect(loadSpy).not.toHaveBeenCalled();
        });
        
        test('应该报告加载进度', async () => {
            assetComponent.addAsset('testTexture', 'texture.png', ASSET_TYPES.TEXTURE);
            
            const progressSpy = jest.fn();
            assetSystem.on('entityLoadProgress', progressSpy);
            
            await assetSystem.loadEntityAssets(entity);
            
            expect(progressSpy).toHaveBeenCalled();
        });
    });
    
    describe('事件处理', () => {
        let entity;
        let assetComponent;
        
        beforeEach(() => {
            assetSystem.initialize();
            
            entity = new Entity('test-entity');
            assetComponent = new AssetComponent(entity);
            entity.addComponent(assetComponent);
            
            assetSystem.addEntity(entity);
        });
        
        test('应该处理资源加载完成事件', () => {
            const eventSpy = jest.fn();
            assetSystem.on('entityAssetLoaded', eventSpy);
            
            assetComponent.emit('assetLoaded', { name: 'test', asset: {} });
            
            expect(eventSpy).toHaveBeenCalledWith({
                entity,
                name: 'test',
                asset: {}
            });
        });
        
        test('应该处理资源加载错误事件', () => {
            const eventSpy = jest.fn();
            assetSystem.on('entityAssetLoadError', eventSpy);
            
            const error = new Error('Load failed');
            assetComponent.emit('assetLoadError', { name: 'test', error });
            
            expect(eventSpy).toHaveBeenCalledWith({
                entity,
                name: 'test',
                error
            });
        });
        
        test('应该处理所有资源加载完成事件', () => {
            const eventSpy = jest.fn();
            assetSystem.on('entityAllAssetsLoaded', eventSpy);
            
            const results = new Map();
            assetComponent.emit('allAssetsLoaded', { results });
            
            expect(eventSpy).toHaveBeenCalledWith({
                entity,
                results
            });
        });
    });
    
    describe('批处理', () => {
        beforeEach(() => {
            // 启用批处理
            assetSystem.options.enableBatchLoading = true;
            assetSystem.initialize();
        });
        
        test('应该将实体添加到加载队列', () => {
            const entity = new Entity('test-entity');
            const assetComponent = new AssetComponent(entity);
            entity.addComponent(assetComponent);
            
            assetComponent.addAsset('testTexture', 'texture.png', ASSET_TYPES.TEXTURE);
            
            assetSystem.addEntity(entity);
            
            expect(assetSystem.loadQueue.length).toBe(1);
            expect(assetSystem.loadQueue[0].entity).toBe(entity);
        });
        
        test('应该处理批次', async () => {
            const entities = [];
            
            // 创建多个实体
            for (let i = 0; i < 3; i++) {
                const entity = new Entity(`entity-${i}`);
                const assetComponent = new AssetComponent(entity);
                entity.addComponent(assetComponent);
                assetComponent.addAsset('texture', `texture${i}.png`, ASSET_TYPES.TEXTURE);
                
                assetSystem.addEntity(entity);
                entities.push(entity);
            }
            
            expect(assetSystem.loadQueue.length).toBe(3);
            
            // 处理批次
            await assetSystem.processBatch();
            
            expect(assetSystem.loadQueue.length).toBeLessThan(3);
        });
    });
    
    describe('清理功能', () => {
        beforeEach(() => {
            assetSystem.initialize();
        });
        
        test('应该清理未使用的资源', () => {
            const entity = new Entity('test-entity');
            const assetComponent = new AssetComponent(entity);
            entity.addComponent(assetComponent);
            
            // 模拟长时间未活动
            entity.lastActiveTime = Date.now() - 120000; // 2分钟前
            
            // 添加一些已加载的资源
            assetComponent.loadedAssets.set('texture1', { dispose: jest.fn() });
            assetComponent.loadedAssets.set('texture2', { dispose: jest.fn() });
            
            assetSystem.addEntity(entity);
            
            const cleanedCount = assetSystem.cleanupUnusedAssets(60000); // 1分钟阈值
            
            expect(cleanedCount).toBe(2);
            expect(assetComponent.loadedAssets.size).toBe(0);
        });
        
        test('应该跳过活跃的实体', () => {
            const entity = new Entity('test-entity');
            const assetComponent = new AssetComponent(entity);
            entity.addComponent(assetComponent);
            
            // 模拟最近活动
            entity.lastActiveTime = Date.now() - 30000; // 30秒前
            
            assetComponent.loadedAssets.set('texture1', { dispose: jest.fn() });
            
            assetSystem.addEntity(entity);
            
            const cleanedCount = assetSystem.cleanupUnusedAssets(60000); // 1分钟阈值
            
            expect(cleanedCount).toBe(0);
            expect(assetComponent.loadedAssets.size).toBe(1);
        });
    });
    
    describe('性能统计', () => {
        beforeEach(() => {
            assetSystem.initialize();
        });
        
        test('应该提供性能统计', () => {
            const stats = assetSystem.getPerformanceStats();
            
            expect(stats).toHaveProperty('assetStats');
            expect(stats).toHaveProperty('assetManagerStats');
            expect(stats).toHaveProperty('queueSize');
            expect(stats.assetStats).toHaveProperty('totalEntities');
            expect(stats.assetStats).toHaveProperty('loadingEntities');
            expect(stats.assetStats).toHaveProperty('loadedEntities');
        });
        
        test('应该提供调试信息', () => {
            const debugInfo = assetSystem.getDebugInfo();
            
            expect(debugInfo).toHaveProperty('assetManagerInitialized');
            expect(debugInfo).toHaveProperty('stats');
            expect(debugInfo).toHaveProperty('queueSize');
            expect(debugInfo).toHaveProperty('batchProcessorActive');
            expect(debugInfo).toHaveProperty('cleanupActive');
        });
        
        test('应该更新实体统计', () => {
            const entity1 = new Entity('entity-1');
            const assetComponent1 = new AssetComponent(entity1);
            entity1.addComponent(assetComponent1);
            
            const entity2 = new Entity('entity-2');
            const assetComponent2 = new AssetComponent(entity2);
            entity2.addComponent(assetComponent2);
            
            assetSystem.addEntity(entity1);
            assetSystem.addEntity(entity2);
            
            assetSystem.updateEntityStats();
            
            expect(assetSystem.stats.totalEntities).toBe(2);
        });
    });
    
    describe('销毁', () => {
        test('应该正确销毁系统', () => {
            assetSystem.initialize();
            
            const disposeSpy = jest.spyOn(mockAssetManager, 'dispose');
            
            assetSystem.dispose();
            
            expect(assetSystem.isDestroyed).toBe(true);
            expect(disposeSpy).toHaveBeenCalled();
            expect(assetSystem.loadQueue).toEqual([]);
        });
        
        test('应该停止所有定时器', () => {
            assetSystem.options.enableBatchLoading = true;
            assetSystem.options.enableAutoCleanup = true;
            assetSystem.initialize();
            
            expect(assetSystem.batchTimer).toBeDefined();
            expect(assetSystem.cleanupTimer).toBeDefined();
            
            assetSystem.dispose();
            
            expect(assetSystem.batchTimer).toBeNull();
            expect(assetSystem.cleanupTimer).toBeNull();
        });
    });
});
