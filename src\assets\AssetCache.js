// src/assets/AssetCache.js
// 资源缓存管理器 - 负责资源的缓存和内存管理

import { EventEmitter } from '../utils/eventEmitter.js';

/**
 * LRU缓存节点
 */
class CacheNode {
    constructor(key, value) {
        this.key = key;
        this.value = value;
        this.prev = null;
        this.next = null;
        this.accessCount = 0;
        this.lastAccessed = Date.now();
        this.size = this.calculateSize(value);
    }
    
    /**
     * 计算节点大小
     * @param {any} value - 缓存值
     * @returns {number} 估算的字节大小
     */
    calculateSize(value) {
        if (value && typeof value.size === 'number') {
            return value.size;
        }
        
        // 简单的大小估算
        try {
            return JSON.stringify(value).length * 2; // Unicode字符占2字节
        } catch {
            return 1024; // 默认1KB
        }
    }
}

/**
 * 资源缓存管理器 - 使用LRU算法管理资源缓存
 */
export class AssetCache extends EventEmitter {
    /**
     * 构造函数
     * @param {Object} [options={}] - 配置选项
     */
    constructor(options = {}) {
        super();
        
        // 配置选项
        this.options = {
            maxSize: 500 * 1024 * 1024, // 500MB
            maxItems: 1000,
            enableAutoCleanup: true,
            cleanupInterval: 60000, // 60秒
            cleanupThreshold: 0.8, // 80%使用率时开始清理
            ttl: 300000, // 5分钟TTL
            enableStats: true,
            ...options
        };
        
        // 缓存存储
        this.cache = new Map();
        
        // LRU双向链表
        this.head = new CacheNode('head', null);
        this.tail = new CacheNode('tail', null);
        this.head.next = this.tail;
        this.tail.prev = this.head;
        
        // 统计信息
        this.stats = {
            hits: 0,
            misses: 0,
            evictions: 0,
            totalSize: 0,
            itemCount: 0,
            cleanupCount: 0,
            lastCleanup: Date.now()
        };
        
        // 清理定时器
        this.cleanupTimer = null;
        
        // 状态标记
        this.isInitialized = false;
        this.isDestroyed = false;
        
        console.log('资源缓存管理器已创建');
    }
    
    /**
     * 初始化缓存
     */
    initialize() {
        if (this.isInitialized) {
            return;
        }
        
        // 启动自动清理
        if (this.options.enableAutoCleanup) {
            this.startAutoCleanup();
        }
        
        this.isInitialized = true;
        console.log('资源缓存管理器初始化完成');
    }
    
    /**
     * 获取缓存项
     * @param {string} key - 缓存键
     * @returns {any|null} 缓存值或null
     */
    get(key) {
        const node = this.cache.get(key);
        
        if (!node) {
            this.stats.misses++;
            return null;
        }
        
        // 检查TTL
        if (this.options.ttl > 0 && Date.now() - node.lastAccessed > this.options.ttl) {
            this.delete(key);
            this.stats.misses++;
            return null;
        }
        
        // 更新访问信息
        node.accessCount++;
        node.lastAccessed = Date.now();
        
        // 移动到链表头部（最近使用）
        this.moveToHead(node);
        
        this.stats.hits++;
        
        return node.value;
    }
    
    /**
     * 设置缓存项
     * @param {string} key - 缓存键
     * @param {any} value - 缓存值
     * @returns {boolean} 是否成功设置
     */
    set(key, value) {
        const existingNode = this.cache.get(key);
        
        if (existingNode) {
            // 更新现有项
            const oldSize = existingNode.size;
            existingNode.value = value;
            existingNode.size = existingNode.calculateSize(value);
            existingNode.lastAccessed = Date.now();
            existingNode.accessCount++;
            
            this.stats.totalSize += (existingNode.size - oldSize);
            this.moveToHead(existingNode);
            
            this.emit('itemUpdated', { key, value, oldSize, newSize: existingNode.size });
            
            return true;
        }
        
        // 创建新节点
        const newNode = new CacheNode(key, value);
        
        // 检查是否需要清理空间
        if (this.needsCleanup(newNode.size)) {
            this.cleanup();
        }
        
        // 再次检查是否有足够空间
        if (this.stats.totalSize + newNode.size > this.options.maxSize) {
            console.warn(`缓存空间不足，无法添加项: ${key}`);
            return false;
        }
        
        // 添加到缓存
        this.cache.set(key, newNode);
        this.addToHead(newNode);
        
        this.stats.totalSize += newNode.size;
        this.stats.itemCount++;
        
        this.emit('itemAdded', { key, value, size: newNode.size });
        
        return true;
    }
    
    /**
     * 删除缓存项
     * @param {string} key - 缓存键
     * @returns {boolean} 是否成功删除
     */
    delete(key) {
        const node = this.cache.get(key);
        
        if (!node) {
            return false;
        }
        
        // 从缓存和链表中移除
        this.cache.delete(key);
        this.removeNode(node);
        
        this.stats.totalSize -= node.size;
        this.stats.itemCount--;
        
        this.emit('itemRemoved', { key, value: node.value, size: node.size });
        
        return true;
    }
    
    /**
     * 检查缓存项是否存在
     * @param {string} key - 缓存键
     * @returns {boolean} 是否存在
     */
    has(key) {
        const node = this.cache.get(key);
        
        if (!node) {
            return false;
        }
        
        // 检查TTL
        if (this.options.ttl > 0 && Date.now() - node.lastAccessed > this.options.ttl) {
            this.delete(key);
            return false;
        }
        
        return true;
    }
    
    /**
     * 清空缓存
     */
    clear() {
        // 释放所有资源
        for (const [key, node] of this.cache) {
            if (node.value && typeof node.value.dispose === 'function') {
                try {
                    node.value.dispose();
                } catch (error) {
                    console.warn(`释放缓存资源时出错: ${key}`, error);
                }
            }
        }
        
        this.cache.clear();
        
        // 重置链表
        this.head.next = this.tail;
        this.tail.prev = this.head;
        
        // 重置统计
        this.stats.totalSize = 0;
        this.stats.itemCount = 0;
        
        this.emit('cleared');
        
        console.log('缓存已清空');
    }
    
    /**
     * 获取缓存统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            ...this.stats,
            hitRate: this.stats.hits / (this.stats.hits + this.stats.misses) || 0,
            utilization: this.stats.totalSize / this.options.maxSize,
            averageItemSize: this.stats.itemCount > 0 ? this.stats.totalSize / this.stats.itemCount : 0
        };
    }
    
    /**
     * 获取当前缓存大小
     * @returns {number} 当前大小（字节）
     */
    getCurrentSize() {
        return this.stats.totalSize;
    }
    
    /**
     * 获取缓存项数量
     * @returns {number} 项数量
     */
    getItemCount() {
        return this.stats.itemCount;
    }
    
    /**
     * 获取所有缓存项的迭代器
     * @returns {Iterator} 缓存项迭代器
     */
    entries() {
        return this.cache.entries();
    }
    
    /**
     * 检查是否需要清理
     * @param {number} [additionalSize=0] - 额外需要的空间
     * @returns {boolean} 是否需要清理
     */
    needsCleanup(additionalSize = 0) {
        const currentUtilization = (this.stats.totalSize + additionalSize) / this.options.maxSize;
        return currentUtilization > this.options.cleanupThreshold || 
               this.stats.itemCount >= this.options.maxItems;
    }
    
    /**
     * 执行缓存清理
     * @param {number} [targetUtilization=0.6] - 目标使用率
     * @returns {number} 清理的项数量
     */
    cleanup(targetUtilization = 0.6) {
        const targetSize = this.options.maxSize * targetUtilization;
        let cleanedCount = 0;
        
        // 从尾部开始清理（最少使用的项）
        let current = this.tail.prev;
        
        while (current !== this.head && this.stats.totalSize > targetSize) {
            const prev = current.prev;
            
            // 删除项
            this.cache.delete(current.key);
            this.removeNode(current);
            
            this.stats.totalSize -= current.size;
            this.stats.itemCount--;
            this.stats.evictions++;
            cleanedCount++;
            
            // 释放资源
            if (current.value && typeof current.value.dispose === 'function') {
                try {
                    current.value.dispose();
                } catch (error) {
                    console.warn(`释放缓存资源时出错: ${current.key}`, error);
                }
            }
            
            this.emit('itemEvicted', { 
                key: current.key, 
                value: current.value, 
                size: current.size,
                reason: 'cleanup'
            });
            
            current = prev;
        }
        
        this.stats.cleanupCount++;
        this.stats.lastCleanup = Date.now();
        
        this.emit('cleanup', { cleanedCount, currentSize: this.stats.totalSize });
        
        console.log(`缓存清理完成，清理了 ${cleanedCount} 个项目`);
        
        return cleanedCount;
    }
    
    /**
     * 启动自动清理
     */
    startAutoCleanup() {
        if (this.cleanupTimer) {
            return;
        }
        
        this.cleanupTimer = setInterval(() => {
            if (this.needsCleanup()) {
                this.cleanup();
            }
        }, this.options.cleanupInterval);
        
        console.log('自动清理已启动');
    }
    
    /**
     * 停止自动清理
     */
    stopAutoCleanup() {
        if (this.cleanupTimer) {
            clearInterval(this.cleanupTimer);
            this.cleanupTimer = null;
            console.log('自动清理已停止');
        }
    }
    
    /**
     * 将节点移动到链表头部
     * @param {CacheNode} node - 节点
     */
    moveToHead(node) {
        this.removeNode(node);
        this.addToHead(node);
    }
    
    /**
     * 添加节点到链表头部
     * @param {CacheNode} node - 节点
     */
    addToHead(node) {
        node.prev = this.head;
        node.next = this.head.next;
        this.head.next.prev = node;
        this.head.next = node;
    }
    
    /**
     * 从链表中移除节点
     * @param {CacheNode} node - 节点
     */
    removeNode(node) {
        node.prev.next = node.next;
        node.next.prev = node.prev;
    }
    
    /**
     * 销毁缓存管理器
     */
    dispose() {
        if (this.isDestroyed) {
            return;
        }
        
        // 停止自动清理
        this.stopAutoCleanup();
        
        // 清空缓存
        this.clear();
        
        this.isDestroyed = true;
        this.emit('disposed');
        
        console.log('资源缓存管理器已销毁');
    }
}
