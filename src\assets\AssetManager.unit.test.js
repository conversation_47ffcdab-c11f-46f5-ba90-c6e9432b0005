// src/assets/AssetManager.unit.test.js
// 资源管理器单元测试

import { AssetManager } from './AssetManager.js';
import { ASSET_TYPES } from './AssetTypes.js';

// Mock Babylon.js Scene
class MockScene {
    constructor() {
        this.meshes = [];
        this.textures = [];
        this.sounds = [];
    }

    dispose() {
        // Mock dispose
    }
}

// Mock资源对象已在jest.mock中定义

// Mock全局对象
global.fetch = jest.fn();
global.performance = {
    now: () => Date.now()
};

// Mock Babylon.js模块
jest.mock('@babylonjs/core/Materials/Textures/texture', () => ({
    Texture: class MockTexture {
        constructor(url) {
            this.url = url;
            this.isReady = false;
            this.onLoadObservable = {
                addOnce: (callback) => {
                    setTimeout(() => {
                        this.isReady = true;
                        callback();
                    }, 10);
                }
            };
            this.onErrorObservable = {
                addOnce: (callback) => {
                    // Mock error handling
                }
            };
        }

        dispose() {
            this.isReady = false;
        }

        getSize() {
            return { width: 512, height: 512 };
        }
    }
}));

jest.mock('@babylonjs/core/Audio/sound', () => ({
    Sound: class MockSound {
        constructor(name, url, scene, onReady, options) {
            this.name = name;
            this.url = url;
            this.isReady = false;

            setTimeout(() => {
                this.isReady = true;
                if (onReady) onReady();
            }, 10);
        }

        dispose() {
            this.isReady = false;
        }
    }
}));

jest.mock('@babylonjs/core/Loading/sceneLoader', () => ({
    SceneLoader: {
        ImportMeshAsync: jest.fn(),
        ImportAnimationsAsync: jest.fn(),
        OnPluginActivatedObservable: {
            add: jest.fn()
        }
    }
}));

jest.mock('@babylonjs/core/Materials/Textures/hdrCubeTexture', () => ({
    HDRCubeTexture: class MockHDRCubeTexture {
        constructor(url) {
            this.url = url;
        }
    }
}));

jest.mock('@babylonjs/core/Materials/Textures/cubeTexture', () => ({
    CubeTexture: class MockCubeTexture {
        constructor(url) {
            this.url = url;
        }
    }
}));

jest.mock('@babylonjs/core/Maths/math.vector', () => ({
    Vector3: class MockVector3 {
        constructor(x, y, z) {
            this.x = x;
            this.y = y;
            this.z = z;
        }
    }
}));

describe('AssetManager', () => {
    let scene;
    let assetManager;

    beforeEach(() => {
        scene = new MockScene();
        assetManager = new AssetManager(scene, {
            enableDebugLogging: false,
            enablePerformanceMonitoring: false
        });

        // 清除fetch mock
        fetch.mockClear();
    });

    afterEach(async () => {
        if (assetManager && !assetManager.isDestroyed) {
            assetManager.dispose();
        }
    });

    describe('初始化', () => {
        test('应该正确创建资源管理器', () => {
            expect(assetManager).toBeDefined();
            expect(assetManager.scene).toBe(scene);
            expect(assetManager.isInitialized).toBe(false);
            expect(assetManager.isDestroyed).toBe(false);
        });

        test('应该正确初始化', async () => {
            await assetManager.initialize();

            expect(assetManager.isInitialized).toBe(true);
            expect(assetManager.loader).toBeDefined();
            expect(assetManager.cache).toBeDefined();
        });

        test('重复初始化应该被忽略', async () => {
            await assetManager.initialize();
            const firstInitTime = assetManager.isInitialized;

            await assetManager.initialize();

            expect(assetManager.isInitialized).toBe(firstInitTime);
        });
    });

    describe('纹理加载', () => {
        beforeEach(async () => {
            await assetManager.initialize();
        });

        test('应该能够加载纹理', async () => {
            const textureUrl = 'test-texture.png';

            const texture = await assetManager.loadTexture(textureUrl);

            expect(texture).toBeDefined();
            expect(texture.url).toBe(textureUrl);
        });

        test('应该缓存加载的纹理', async () => {
            const textureUrl = 'test-texture.png';

            const texture1 = await assetManager.loadTexture(textureUrl);
            const texture2 = await assetManager.loadTexture(textureUrl);

            expect(texture1).toBe(texture2);
            expect(assetManager.stats.cacheHits).toBe(1);
        });

        test('应该支持强制重新加载', async () => {
            const textureUrl = 'test-texture.png';

            const texture1 = await assetManager.loadTexture(textureUrl);
            const texture2 = await assetManager.loadTexture(textureUrl, { forceReload: true });

            expect(texture1).not.toBe(texture2);
        });
    });

    describe('音频加载', () => {
        beforeEach(async () => {
            await assetManager.initialize();
        });

        test('应该能够加载音频', async () => {
            const soundUrl = 'test-sound.mp3';

            const sound = await assetManager.loadSound(soundUrl);

            expect(sound).toBeDefined();
            expect(sound.url).toBe(soundUrl);
        });

        test('应该支持音频选项', async () => {
            const soundUrl = 'test-sound.mp3';
            const options = {
                loop: true,
                volume: 0.5,
                spatialSound: true
            };

            const sound = await assetManager.loadSound(soundUrl, options);

            expect(sound).toBeDefined();
        });
    });

    describe('配置文件加载', () => {
        beforeEach(async () => {
            await assetManager.initialize();
        });

        test('应该能够加载JSON配置', async () => {
            const configUrl = 'test-config.json';
            const mockConfig = { test: 'data', value: 123 };

            fetch.mockResolvedValueOnce({
                ok: true,
                text: () => Promise.resolve(JSON.stringify(mockConfig))
            });

            const config = await assetManager.loadConfig(configUrl);

            expect(config).toEqual(mockConfig);
            expect(fetch).toHaveBeenCalledWith(configUrl);
        });

        test('应该处理网络错误', async () => {
            const configUrl = 'test-config.json';

            fetch.mockResolvedValueOnce({
                ok: false,
                status: 404,
                statusText: 'Not Found'
            });

            await expect(assetManager.loadConfig(configUrl)).rejects.toThrow();
        });

        test('应该处理JSON解析错误', async () => {
            const configUrl = 'test-config.json';

            fetch.mockResolvedValueOnce({
                ok: true,
                text: () => Promise.resolve('invalid json')
            });

            await expect(assetManager.loadConfig(configUrl)).rejects.toThrow();
        });
    });

    describe('预加载', () => {
        beforeEach(async () => {
            await assetManager.initialize();
        });

        test('应该能够预加载资源列表', async () => {
            const assetList = [
                { url: 'texture1.png', type: ASSET_TYPES.TEXTURE },
                { url: 'texture2.png', type: ASSET_TYPES.TEXTURE },
                { url: 'sound1.mp3', type: ASSET_TYPES.SOUND }
            ];

            const results = await assetManager.preloadAssets(assetList);

            expect(results).toHaveLength(3);
            expect(results.every(r => r.success)).toBe(true);
        });

        test('应该报告预加载进度', async () => {
            const assetList = [
                { url: 'texture1.png', type: ASSET_TYPES.TEXTURE },
                { url: 'texture2.png', type: ASSET_TYPES.TEXTURE }
            ];

            const progressUpdates = [];

            await assetManager.preloadAssets(assetList, (progress) => {
                progressUpdates.push(progress);
            });

            expect(progressUpdates.length).toBeGreaterThan(0);
            expect(progressUpdates[progressUpdates.length - 1].completed).toBe(2);
        });
    });

    describe('资源管理', () => {
        beforeEach(async () => {
            await assetManager.initialize();
        });

        test('应该能够获取已加载的资源', async () => {
            const textureUrl = 'test-texture.png';
            const texture = await assetManager.loadTexture(textureUrl);
            const assetId = assetManager.generateAssetId(textureUrl, ASSET_TYPES.TEXTURE);

            const retrievedTexture = assetManager.getAsset(assetId);

            expect(retrievedTexture).toBe(texture);
        });

        test('应该能够释放资源', async () => {
            const textureUrl = 'test-texture.png';
            await assetManager.loadTexture(textureUrl);
            const assetId = assetManager.generateAssetId(textureUrl, ASSET_TYPES.TEXTURE);

            const released = assetManager.releaseAsset(assetId);

            expect(released).toBe(true);
            expect(assetManager.getAsset(assetId)).toBeNull();
        });

        test('应该能够获取加载进度', async () => {
            const progress = assetManager.getLoadingProgress();

            expect(progress).toHaveProperty('activeLoads');
            expect(progress).toHaveProperty('queuedLoads');
            expect(progress).toHaveProperty('totalPending');
            expect(progress).toHaveProperty('isLoading');
        });

        test('应该能够获取性能统计', async () => {
            const stats = assetManager.getPerformanceStats();

            expect(stats).toHaveProperty('totalLoaded');
            expect(stats).toHaveProperty('totalFailed');
            expect(stats).toHaveProperty('cacheHits');
            expect(stats).toHaveProperty('cacheMisses');
            expect(stats).toHaveProperty('averageLoadTime');
        });
    });

    describe('错误处理', () => {
        beforeEach(async () => {
            await assetManager.initialize();
        });

        test('未初始化时加载应该抛出错误', async () => {
            const uninitializedManager = new AssetManager(scene);

            await expect(uninitializedManager.loadTexture('test.png')).rejects.toThrow('资源管理器未初始化');
        });

        test('已销毁时加载应该抛出错误', async () => {
            assetManager.dispose();

            await expect(assetManager.loadTexture('test.png')).rejects.toThrow('资源管理器已销毁');
        });
    });

    describe('清理和销毁', () => {
        beforeEach(async () => {
            await assetManager.initialize();
        });

        test('应该能够清理旧资源', async () => {
            // 加载一些资源
            await assetManager.loadTexture('texture1.png');
            await assetManager.loadTexture('texture2.png');

            // 清理资源
            const cleanedCount = assetManager.cleanup(0); // 立即清理所有资源

            expect(cleanedCount).toBeGreaterThan(0);
        });

        test('应该能够正确销毁', () => {
            assetManager.dispose();

            expect(assetManager.isDestroyed).toBe(true);
        });

        test('重复销毁应该被忽略', () => {
            assetManager.dispose();
            const firstDestroyState = assetManager.isDestroyed;

            assetManager.dispose();

            expect(assetManager.isDestroyed).toBe(firstDestroyState);
        });
    });

    describe('事件系统', () => {
        beforeEach(async () => {
            await assetManager.initialize();
        });

        test('应该触发加载开始事件', async () => {
            const loadStartSpy = jest.fn();
            assetManager.on('loadStart', loadStartSpy);

            await assetManager.loadTexture('test-texture.png');

            expect(loadStartSpy).toHaveBeenCalled();
        });

        test('应该触发加载完成事件', async () => {
            const loadCompleteSpy = jest.fn();
            assetManager.on('loadComplete', loadCompleteSpy);

            await assetManager.loadTexture('test-texture.png');

            expect(loadCompleteSpy).toHaveBeenCalled();
        });

        test('应该触发缓存命中事件', async () => {
            const cacheHitSpy = jest.fn();
            assetManager.on('cacheHit', cacheHitSpy);

            await assetManager.loadTexture('test-texture.png');
            await assetManager.loadTexture('test-texture.png'); // 第二次应该命中缓存

            expect(cacheHitSpy).toHaveBeenCalled();
        });
    });
});
