// src/ecs/systems/AssetSystem.js
// 资源系统 - 管理实体的资源加载和生命周期

import { System } from '../System.js';
import { AssetComponent } from '../components/AssetComponent.js';
import { AssetManager } from '../../assets/AssetManager.js';

/**
 * 资源系统 - 处理具有AssetComponent的实体
 * 负责资源的自动加载、更新和清理
 */
export class AssetSystem extends System {
    /**
     * 构造函数
     * @param {BABYLON.Scene} scene - Babylon.js场景实例
     * @param {Object} [options={}] - 系统选项
     */
    constructor(scene, options = {}) {
        super(scene, options);
        
        // 系统配置
        this.options = {
            // 自动加载配置
            autoLoadOnAdd: true,
            autoLoadDelay: 100, // 延迟100ms加载，避免频繁操作
            
            // 批处理配置
            enableBatchLoading: true,
            batchSize: 5,
            batchInterval: 200,
            
            // 性能配置
            maxConcurrentLoads: 3,
            enablePreloading: true,
            
            // 清理配置
            enableAutoCleanup: true,
            cleanupInterval: 30000, // 30秒
            unusedThreshold: 60000, // 60秒未使用则清理
            
            ...options
        };
        
        // 系统需要的组件类型
        this.requiredComponents = [AssetComponent];
        
        // 资源管理器
        this.assetManager = null;
        
        // 加载队列
        this.loadQueue = [];
        this.batchTimer = null;
        
        // 性能统计
        this.stats = {
            totalEntities: 0,
            loadingEntities: 0,
            loadedEntities: 0,
            failedEntities: 0,
            totalAssets: 0,
            loadedAssets: 0,
            failedAssets: 0,
            averageLoadTime: 0,
            lastCleanup: Date.now()
        };
        
        // 清理定时器
        this.cleanupTimer = null;
        
        console.log('资源系统已创建');
    }
    
    /**
     * 系统初始化
     */
    onInitialize() {
        super.onInitialize();
        
        // 创建资源管理器
        this.assetManager = new AssetManager(this.scene, {
            maxConcurrentLoads: this.options.maxConcurrentLoads,
            enablePreloading: this.options.enablePreloading,
            enableDebugLogging: false
        });
        
        // 初始化资源管理器
        this.assetManager.initialize().then(() => {
            console.log('资源系统中的资源管理器初始化完成');
        }).catch(error => {
            console.error('资源系统中的资源管理器初始化失败:', error);
        });
        
        // 监听资源管理器事件
        this.bindAssetManagerEvents();
        
        // 启动批处理
        if (this.options.enableBatchLoading) {
            this.startBatchProcessor();
        }
        
        // 启动自动清理
        if (this.options.enableAutoCleanup) {
            this.startAutoCleanup();
        }
        
        console.log('资源系统初始化完成');
    }
    
    /**
     * 绑定资源管理器事件
     */
    bindAssetManagerEvents() {
        this.assetManager.on('loadStart', (data) => {
            this.emit('assetLoadStart', data);
        });
        
        this.assetManager.on('loadProgress', (data) => {
            this.emit('assetLoadProgress', data);
        });
        
        this.assetManager.on('loadComplete', (data) => {
            this.stats.loadedAssets++;
            this.emit('assetLoadComplete', data);
        });
        
        this.assetManager.on('loadError', (data) => {
            this.stats.failedAssets++;
            this.emit('assetLoadError', data);
        });
        
        this.assetManager.on('memoryPressure', (data) => {
            this.handleMemoryPressure(data);
        });
    }
    
    /**
     * 实体添加到系统时调用
     * @param {Entity} entity - 被添加的实体
     */
    onEntityAdded(entity) {
        super.onEntityAdded(entity);
        
        const assetComponent = entity.getComponent(AssetComponent);
        if (!assetComponent) {
            return;
        }
        
        // 设置资源管理器引用
        assetComponent.setAssetManager(this.assetManager);
        
        // 监听组件事件
        this.bindComponentEvents(assetComponent);
        
        // 自动加载资源
        if (this.options.autoLoadOnAdd && assetComponent.assetConfigs.size > 0) {
            this.scheduleLoad(entity);
        }
        
        this.stats.totalEntities++;
        this.updateEntityStats();
        
        console.log(`实体已添加到资源系统: ${entity.id}`);
    }
    
    /**
     * 实体从系统移除时调用
     * @param {Entity} entity - 被移除的实体
     */
    onEntityRemoved(entity) {
        super.onEntityRemoved(entity);
        
        const assetComponent = entity.getComponent(AssetComponent);
        if (assetComponent) {
            // 解绑组件事件
            this.unbindComponentEvents(assetComponent);
            
            // 可选择性释放资源
            if (assetComponent.autoRelease) {
                assetComponent.releaseAllAssets();
            }
        }
        
        this.stats.totalEntities--;
        this.updateEntityStats();
        
        console.log(`实体已从资源系统移除: ${entity.id}`);
    }
    
    /**
     * 绑定组件事件
     * @param {AssetComponent} component - 资源组件
     */
    bindComponentEvents(component) {
        component.on('assetLoaded', (data) => {
            this.handleAssetLoaded(component.entity, data);
        });
        
        component.on('assetLoadError', (data) => {
            this.handleAssetLoadError(component.entity, data);
        });
        
        component.on('allAssetsLoaded', (data) => {
            this.handleAllAssetsLoaded(component.entity, data);
        });
    }
    
    /**
     * 解绑组件事件
     * @param {AssetComponent} component - 资源组件
     */
    unbindComponentEvents(component) {
        component.removeAllListeners('assetLoaded');
        component.removeAllListeners('assetLoadError');
        component.removeAllListeners('allAssetsLoaded');
    }
    
    /**
     * 调度加载任务
     * @param {Entity} entity - 实体
     */
    scheduleLoad(entity) {
        if (this.options.enableBatchLoading) {
            // 添加到批处理队列
            this.loadQueue.push({
                entity,
                scheduledAt: Date.now()
            });
        } else {
            // 立即加载
            setTimeout(() => {
                this.loadEntityAssets(entity);
            }, this.options.autoLoadDelay);
        }
    }
    
    /**
     * 加载实体资源
     * @param {Entity} entity - 实体
     */
    async loadEntityAssets(entity) {
        const assetComponent = entity.getComponent(AssetComponent);
        if (!assetComponent || assetComponent.isLoading) {
            return;
        }
        
        this.stats.loadingEntities++;
        
        try {
            const startTime = performance.now();
            
            await assetComponent.loadAllAssets((progress) => {
                this.emit('entityLoadProgress', {
                    entity,
                    progress: progress.progress,
                    current: progress.name,
                    completed: progress.completed,
                    total: progress.total
                });
            });
            
            const loadTime = performance.now() - startTime;
            this.updateLoadTimeStats(loadTime);
            
            this.stats.loadedEntities++;
            this.stats.loadingEntities--;
            
            this.emit('entityAssetsLoaded', { entity, loadTime });
            
            console.log(`实体资源加载完成: ${entity.id} (${loadTime.toFixed(2)}ms)`);
        } catch (error) {
            this.stats.failedEntities++;
            this.stats.loadingEntities--;
            
            this.emit('entityAssetsLoadError', { entity, error });
            
            console.error(`实体资源加载失败: ${entity.id}`, error);
        }
    }
    
    /**
     * 处理资源加载完成
     * @param {Entity} entity - 实体
     * @param {Object} data - 加载数据
     */
    handleAssetLoaded(entity, data) {
        this.emit('entityAssetLoaded', { entity, ...data });
    }
    
    /**
     * 处理资源加载错误
     * @param {Entity} entity - 实体
     * @param {Object} data - 错误数据
     */
    handleAssetLoadError(entity, data) {
        this.emit('entityAssetLoadError', { entity, ...data });
    }
    
    /**
     * 处理所有资源加载完成
     * @param {Entity} entity - 实体
     * @param {Object} data - 加载结果数据
     */
    handleAllAssetsLoaded(entity, data) {
        this.emit('entityAllAssetsLoaded', { entity, ...data });
    }
    
    /**
     * 处理内存压力
     * @param {Object} data - 内存使用数据
     */
    handleMemoryPressure(data) {
        console.warn('检测到内存压力，开始清理未使用的资源', data);
        
        // 清理长时间未使用的实体资源
        this.cleanupUnusedAssets(this.options.unusedThreshold / 2); // 使用更短的阈值
        
        this.emit('memoryPressureHandled', data);
    }
    
    /**
     * 启动批处理器
     */
    startBatchProcessor() {
        if (this.batchTimer) {
            return;
        }
        
        this.batchTimer = setInterval(() => {
            this.processBatch();
        }, this.options.batchInterval);
        
        console.log('资源批处理器已启动');
    }
    
    /**
     * 处理批次
     */
    async processBatch() {
        if (this.loadQueue.length === 0) {
            return;
        }
        
        const batch = this.loadQueue.splice(0, this.options.batchSize);
        
        // 并行处理批次中的实体
        const loadPromises = batch.map(item => this.loadEntityAssets(item.entity));
        
        try {
            await Promise.all(loadPromises);
        } catch (error) {
            console.error('批处理加载出错:', error);
        }
    }
    
    /**
     * 启动自动清理
     */
    startAutoCleanup() {
        if (this.cleanupTimer) {
            return;
        }
        
        this.cleanupTimer = setInterval(() => {
            this.cleanupUnusedAssets();
        }, this.options.cleanupInterval);
        
        console.log('资源自动清理已启动');
    }
    
    /**
     * 清理未使用的资源
     * @param {number} [threshold] - 未使用时间阈值
     */
    cleanupUnusedAssets(threshold = this.options.unusedThreshold) {
        let cleanedCount = 0;
        const now = Date.now();
        
        for (const entity of this.entities) {
            const assetComponent = entity.getComponent(AssetComponent);
            if (!assetComponent) {
                continue;
            }
            
            // 检查实体是否长时间未活动
            if (entity.lastActiveTime && (now - entity.lastActiveTime) > threshold) {
                const releasedAssets = assetComponent.loadedAssets.size;
                assetComponent.releaseAllAssets();
                cleanedCount += releasedAssets;
                
                console.log(`清理实体未使用资源: ${entity.id} (${releasedAssets} 个资源)`);
            }
        }
        
        this.stats.lastCleanup = now;
        
        if (cleanedCount > 0) {
            this.emit('assetsCleanedUp', { cleanedCount, threshold });
            console.log(`资源清理完成，共清理 ${cleanedCount} 个资源`);
        }
    }
    
    /**
     * 更新加载时间统计
     * @param {number} loadTime - 加载时间
     */
    updateLoadTimeStats(loadTime) {
        // 简单的移动平均
        this.stats.averageLoadTime = (this.stats.averageLoadTime * 0.9) + (loadTime * 0.1);
    }
    
    /**
     * 更新实体统计
     */
    updateEntityStats() {
        // 统计各种状态的实体数量
        let loadingCount = 0;
        let loadedCount = 0;
        let failedCount = 0;
        let totalAssets = 0;
        
        for (const entity of this.entities) {
            const assetComponent = entity.getComponent(AssetComponent);
            if (assetComponent) {
                if (assetComponent.isLoading) {
                    loadingCount++;
                }
                
                totalAssets += assetComponent.assetConfigs.size;
                
                if (assetComponent.loadedAssets.size === assetComponent.assetConfigs.size) {
                    loadedCount++;
                } else if (assetComponent.loadErrors.length > 0) {
                    failedCount++;
                }
            }
        }
        
        this.stats.loadingEntities = loadingCount;
        this.stats.loadedEntities = loadedCount;
        this.stats.failedEntities = failedCount;
        this.stats.totalAssets = totalAssets;
    }
    
    /**
     * 获取系统性能统计
     * @returns {Object} 性能统计
     */
    getPerformanceStats() {
        return {
            ...super.getPerformanceStats(),
            assetStats: { ...this.stats },
            assetManagerStats: this.assetManager ? this.assetManager.getPerformanceStats() : null,
            queueSize: this.loadQueue.length
        };
    }
    
    /**
     * 获取系统调试信息
     * @returns {Object} 调试信息
     */
    getDebugInfo() {
        return {
            ...super.getDebugInfo(),
            assetManagerInitialized: this.assetManager?.isInitialized || false,
            stats: this.stats,
            queueSize: this.loadQueue.length,
            batchProcessorActive: !!this.batchTimer,
            cleanupActive: !!this.cleanupTimer
        };
    }
    
    /**
     * 系统销毁
     */
    dispose() {
        // 停止定时器
        if (this.batchTimer) {
            clearInterval(this.batchTimer);
            this.batchTimer = null;
        }
        
        if (this.cleanupTimer) {
            clearInterval(this.cleanupTimer);
            this.cleanupTimer = null;
        }
        
        // 销毁资源管理器
        if (this.assetManager) {
            this.assetManager.dispose();
            this.assetManager = null;
        }
        
        // 清空队列
        this.loadQueue = [];
        
        super.dispose();
        
        console.log('资源系统已销毁');
    }
}
