// src/assets/AssetTypes.js
// 资源类型定义和配置

/**
 * 资源类型常量
 */
export const ASSET_TYPES = {
    MODEL: 'model',
    TEXTURE: 'texture',
    SOUND: 'sound',
    CONFIG: 'config',
    ANIMATION: 'animation',
    MATERIAL: 'material',
    SHADER: 'shader',
    FONT: 'font',
    VIDEO: 'video'
};

/**
 * 支持的文件扩展名映射
 */
export const ASSET_EXTENSIONS = {
    [ASSET_TYPES.MODEL]: ['.glb', '.gltf', '.babylon', '.obj', '.fbx'],
    [ASSET_TYPES.TEXTURE]: ['.jpg', '.jpeg', '.png', '.dds', '.ktx', '.ktx2', '.hdr', '.exr'],
    [ASSET_TYPES.SOUND]: ['.mp3', '.wav', '.ogg', '.m4a', '.aac'],
    [ASSET_TYPES.CONFIG]: ['.json', '.xml', '.yaml', '.yml'],
    [ASSET_TYPES.ANIMATION]: ['.babylon', '.glb', '.gltf'],
    [ASSET_TYPES.MATERIAL]: ['.json', '.babylon'],
    [ASSET_TYPES.SHADER]: ['.glsl', '.hlsl', '.fx', '.vert', '.frag'],
    [ASSET_TYPES.FONT]: ['.ttf', '.otf', '.woff', '.woff2'],
    [ASSET_TYPES.VIDEO]: ['.mp4', '.webm', '.ogg']
};

/**
 * 默认资源加载选项
 */
export const DEFAULT_ASSET_OPTIONS = {
    [ASSET_TYPES.MODEL]: {
        // 模型加载选项
        importMeshes: true,
        importAnimations: true,
        importMaterials: true,
        importTextures: true,
        optimizeForSize: false,
        optimizeForSpeed: true,
        mergeMeshes: false,
        scaleFactor: 1.0,
        rotationOffset: { x: 0, y: 0, z: 0 },
        positionOffset: { x: 0, y: 0, z: 0 }
    },
    
    [ASSET_TYPES.TEXTURE]: {
        // 纹理加载选项
        generateMipMaps: true,
        samplingMode: 3, // TRILINEAR_SAMPLINGMODE
        wrapU: 1, // WRAP_ADDRESSMODE
        wrapV: 1, // WRAP_ADDRESSMODE
        format: 5, // RGBA format
        invertY: true,
        premultiplyAlpha: false,
        deleteBuffer: true
    },
    
    [ASSET_TYPES.SOUND]: {
        // 音频加载选项
        autoplay: false,
        loop: false,
        volume: 1.0,
        playbackRate: 1.0,
        spatialSound: false,
        maxDistance: 100,
        rolloffFactor: 1,
        refDistance: 1,
        distanceModel: 'linear',
        streaming: false
    },
    
    [ASSET_TYPES.CONFIG]: {
        // 配置文件加载选项
        parseJSON: true,
        validateSchema: false,
        schema: null,
        encoding: 'utf-8'
    },
    
    [ASSET_TYPES.ANIMATION]: {
        // 动画加载选项
        autoStart: false,
        loop: false,
        speedRatio: 1.0,
        enableBlending: true,
        blendingSpeed: 0.01
    },
    
    [ASSET_TYPES.MATERIAL]: {
        // 材质加载选项
        checkReadyOnlyOnce: false,
        checkReadyOnEveryCall: true,
        disableDepthWrite: false,
        disableColorWrite: false,
        forceDepthWrite: false
    },
    
    [ASSET_TYPES.SHADER]: {
        // 着色器加载选项
        defines: [],
        fallbacks: null,
        onCompiled: null,
        onError: null,
        indexParameters: null
    },
    
    [ASSET_TYPES.FONT]: {
        // 字体加载选项
        size: 16,
        resolution: 64,
        color: '#ffffff',
        outlineWidth: 0,
        outlineColor: '#000000'
    },
    
    [ASSET_TYPES.VIDEO]: {
        // 视频加载选项
        autoplay: false,
        loop: false,
        muted: false,
        controls: false,
        poster: null,
        generateMipMaps: false
    }
};

/**
 * 资源优先级定义
 */
export const ASSET_PRIORITY = {
    CRITICAL: 0,    // 关键资源，立即加载
    HIGH: 1,        // 高优先级，优先加载
    NORMAL: 2,      // 普通优先级，正常加载
    LOW: 3,         // 低优先级，延迟加载
    BACKGROUND: 4   // 后台加载，空闲时加载
};

/**
 * 资源状态定义
 */
export const ASSET_STATUS = {
    PENDING: 'pending',       // 等待加载
    LOADING: 'loading',       // 正在加载
    LOADED: 'loaded',         // 加载完成
    ERROR: 'error',           // 加载失败
    CACHED: 'cached',         // 已缓存
    DISPOSED: 'disposed'      // 已释放
};

/**
 * 缓存策略定义
 */
export const CACHE_STRATEGY = {
    MEMORY: 'memory',         // 内存缓存
    PERSISTENT: 'persistent', // 持久化缓存
    TEMPORARY: 'temporary',   // 临时缓存
    NO_CACHE: 'no_cache'     // 不缓存
};

/**
 * 根据文件扩展名获取资源类型
 * @param {string} url - 资源URL或文件名
 * @returns {string|null} 资源类型或null
 */
export function getAssetTypeFromUrl(url) {
    const extension = url.toLowerCase().substring(url.lastIndexOf('.'));
    
    for (const [type, extensions] of Object.entries(ASSET_EXTENSIONS)) {
        if (extensions.includes(extension)) {
            return type;
        }
    }
    
    return null;
}

/**
 * 验证资源类型是否支持
 * @param {string} type - 资源类型
 * @returns {boolean} 是否支持
 */
export function isAssetTypeSupported(type) {
    return Object.values(ASSET_TYPES).includes(type);
}

/**
 * 获取资源类型的默认选项
 * @param {string} type - 资源类型
 * @returns {Object} 默认选项
 */
export function getDefaultOptionsForType(type) {
    return DEFAULT_ASSET_OPTIONS[type] || {};
}

/**
 * 验证文件扩展名是否支持
 * @param {string} extension - 文件扩展名
 * @returns {boolean} 是否支持
 */
export function isExtensionSupported(extension) {
    const normalizedExt = extension.toLowerCase();
    if (!normalizedExt.startsWith('.')) {
        normalizedExt = '.' + normalizedExt;
    }
    
    for (const extensions of Object.values(ASSET_EXTENSIONS)) {
        if (extensions.includes(normalizedExt)) {
            return true;
        }
    }
    
    return false;
}

/**
 * 资源配置验证器
 */
export class AssetConfigValidator {
    /**
     * 验证资源配置
     * @param {Object} config - 资源配置
     * @returns {Object} 验证结果
     */
    static validate(config) {
        const errors = [];
        const warnings = [];
        
        // 检查必需字段
        if (!config.url) {
            errors.push('缺少资源URL');
        }
        
        if (!config.type) {
            // 尝试从URL推断类型
            const inferredType = getAssetTypeFromUrl(config.url);
            if (inferredType) {
                config.type = inferredType;
                warnings.push(`从URL推断资源类型: ${inferredType}`);
            } else {
                errors.push('无法确定资源类型');
            }
        }
        
        // 验证资源类型
        if (config.type && !isAssetTypeSupported(config.type)) {
            errors.push(`不支持的资源类型: ${config.type}`);
        }
        
        // 验证优先级
        if (config.priority !== undefined && !Object.values(ASSET_PRIORITY).includes(config.priority)) {
            warnings.push(`无效的优先级值: ${config.priority}，使用默认值`);
            config.priority = ASSET_PRIORITY.NORMAL;
        }
        
        // 验证缓存策略
        if (config.cacheStrategy && !Object.values(CACHE_STRATEGY).includes(config.cacheStrategy)) {
            warnings.push(`无效的缓存策略: ${config.cacheStrategy}，使用默认值`);
            config.cacheStrategy = CACHE_STRATEGY.MEMORY;
        }
        
        return {
            isValid: errors.length === 0,
            errors,
            warnings,
            config
        };
    }
    
    /**
     * 验证资源列表配置
     * @param {Array} assetList - 资源列表
     * @returns {Object} 验证结果
     */
    static validateList(assetList) {
        if (!Array.isArray(assetList)) {
            return {
                isValid: false,
                errors: ['资源列表必须是数组'],
                warnings: [],
                validAssets: [],
                invalidAssets: []
            };
        }
        
        const validAssets = [];
        const invalidAssets = [];
        const allErrors = [];
        const allWarnings = [];
        
        assetList.forEach((asset, index) => {
            const result = this.validate(asset);
            
            if (result.isValid) {
                validAssets.push(result.config);
            } else {
                invalidAssets.push({ index, asset, errors: result.errors });
                allErrors.push(`资源 ${index}: ${result.errors.join(', ')}`);
            }
            
            if (result.warnings.length > 0) {
                allWarnings.push(`资源 ${index}: ${result.warnings.join(', ')}`);
            }
        });
        
        return {
            isValid: invalidAssets.length === 0,
            errors: allErrors,
            warnings: allWarnings,
            validAssets,
            invalidAssets
        };
    }
}

/**
 * 资源预设配置
 */
export const ASSET_PRESETS = {
    // 角色模型预设
    CHARACTER_MODEL: {
        type: ASSET_TYPES.MODEL,
        priority: ASSET_PRIORITY.HIGH,
        cacheStrategy: CACHE_STRATEGY.MEMORY,
        options: {
            ...DEFAULT_ASSET_OPTIONS.MODEL,
            importAnimations: true,
            mergeMeshes: false,
            optimizeForSpeed: true
        }
    },
    
    // 环境纹理预设
    ENVIRONMENT_TEXTURE: {
        type: ASSET_TYPES.TEXTURE,
        priority: ASSET_PRIORITY.NORMAL,
        cacheStrategy: CACHE_STRATEGY.PERSISTENT,
        options: {
            ...DEFAULT_ASSET_OPTIONS.TEXTURE,
            generateMipMaps: true,
            format: 5 // RGBA
        }
    },
    
    // UI纹理预设
    UI_TEXTURE: {
        type: ASSET_TYPES.TEXTURE,
        priority: ASSET_PRIORITY.HIGH,
        cacheStrategy: CACHE_STRATEGY.MEMORY,
        options: {
            ...DEFAULT_ASSET_OPTIONS.TEXTURE,
            generateMipMaps: false,
            samplingMode: 1 // NEAREST_SAMPLINGMODE
        }
    },
    
    // 背景音乐预设
    BACKGROUND_MUSIC: {
        type: ASSET_TYPES.SOUND,
        priority: ASSET_PRIORITY.LOW,
        cacheStrategy: CACHE_STRATEGY.TEMPORARY,
        options: {
            ...DEFAULT_ASSET_OPTIONS.SOUND,
            loop: true,
            volume: 0.5,
            streaming: true
        }
    },
    
    // 音效预设
    SOUND_EFFECT: {
        type: ASSET_TYPES.SOUND,
        priority: ASSET_PRIORITY.NORMAL,
        cacheStrategy: CACHE_STRATEGY.MEMORY,
        options: {
            ...DEFAULT_ASSET_OPTIONS.SOUND,
            volume: 0.8,
            spatialSound: true
        }
    }
};
