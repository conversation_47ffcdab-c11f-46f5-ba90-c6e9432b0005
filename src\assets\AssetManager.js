// src/assets/AssetManager.js
// 资源管理系统 - 负责游戏资源的加载、缓存和管理

import { EventEmitter } from '../utils/eventEmitter.js';
import { AssetLoader } from './AssetLoader.js';
import { AssetCache } from './AssetCache.js';
import { ASSET_TYPES, ASSET_EXTENSIONS, DEFAULT_ASSET_OPTIONS } from './AssetTypes.js';

/**
 * 资源管理器 - 统一管理游戏中的所有资源
 * 提供异步加载、缓存管理、内存优化等功能
 */
export class AssetManager extends EventEmitter {
    /**
     * 构造函数
     * @param {BABYLON.Scene} scene - Babylon.js场景实例
     * @param {Object} [options={}] - 配置选项
     */
    constructor(scene, options = {}) {
        super();

        // Babylon.js场景引用
        this.scene = scene;

        // 配置选项
        this.options = {
            // 缓存配置
            maxCacheSize: 500 * 1024 * 1024, // 500MB
            enableAutoCleanup: true,
            cleanupInterval: 60000, // 60秒

            // 加载配置
            maxConcurrentLoads: 6,
            retryAttempts: 3,
            retryDelay: 1000,

            // 预加载配置
            enablePreloading: true,
            preloadBatchSize: 5,

            // 调试配置
            enableDebugLogging: false,
            enablePerformanceMonitoring: true,

            ...options
        };

        // 资源加载器
        this.loader = new AssetLoader(scene, {
            maxConcurrentLoads: this.options.maxConcurrentLoads,
            retryAttempts: this.options.retryAttempts,
            retryDelay: this.options.retryDelay,
            enableDebugLogging: this.options.enableDebugLogging
        });

        // 资源缓存
        this.cache = new AssetCache({
            maxSize: this.options.maxCacheSize,
            enableAutoCleanup: this.options.enableAutoCleanup,
            cleanupInterval: this.options.cleanupInterval
        });

        // 加载状态管理
        this.loadingTasks = new Map(); // 正在加载的任务
        this.loadingQueue = []; // 加载队列
        this.preloadQueue = []; // 预加载队列

        // 性能统计
        this.stats = {
            totalLoaded: 0,
            totalFailed: 0,
            totalSize: 0,
            cacheHits: 0,
            cacheMisses: 0,
            averageLoadTime: 0,
            loadTimes: []
        };

        // 状态标记
        this.isInitialized = false;
        this.isDestroyed = false;

        // 绑定事件处理器
        this.bindEvents();

        console.log('资源管理器已创建');
    }

    /**
     * 初始化资源管理器
     */
    async initialize() {
        if (this.isInitialized) {
            console.warn('资源管理器已经初始化');
            return;
        }

        try {
            // 初始化加载器
            await this.loader.initialize();

            // 初始化缓存
            this.cache.initialize();

            // 启动预加载处理
            if (this.options.enablePreloading) {
                this.startPreloadProcessor();
            }

            // 启动性能监控
            if (this.options.enablePerformanceMonitoring) {
                this.startPerformanceMonitoring();
            }

            this.isInitialized = true;
            this.emit('initialized', { manager: this });

            console.log('资源管理器初始化完成');
        } catch (error) {
            console.error('资源管理器初始化失败:', error);
            throw error;
        }
    }

    /**
     * 绑定事件处理器
     */
    bindEvents() {
        // 监听加载器事件
        this.loader.on('loadStart', (data) => {
            this.emit('loadStart', data);
        });

        this.loader.on('loadProgress', (data) => {
            this.emit('loadProgress', data);
        });

        this.loader.on('loadComplete', (data) => {
            this.handleLoadComplete(data);
        });

        this.loader.on('loadError', (data) => {
            this.handleLoadError(data);
        });

        // 监听缓存事件
        this.cache.on('itemAdded', (data) => {
            this.stats.totalSize += data.size || 0;
            this.emit('assetCached', data);
        });

        this.cache.on('itemRemoved', (data) => {
            this.stats.totalSize -= data.size || 0;
            this.emit('assetEvicted', data);
        });

        this.cache.on('cleanup', (data) => {
            this.emit('cacheCleanup', data);
        });
    }

    /**
     * 加载模型资源
     * @param {string} url - 模型文件URL
     * @param {Object} [options={}] - 加载选项
     * @returns {Promise<Object>} 加载的模型资源
     */
    async loadModel(url, options = {}) {
        return this.loadAsset(url, ASSET_TYPES.MODEL, {
            ...DEFAULT_ASSET_OPTIONS.MODEL,
            ...options
        });
    }

    /**
     * 加载纹理资源
     * @param {string} url - 纹理文件URL
     * @param {Object} [options={}] - 加载选项
     * @returns {Promise<BABYLON.Texture>} 加载的纹理资源
     */
    async loadTexture(url, options = {}) {
        return this.loadAsset(url, ASSET_TYPES.TEXTURE, {
            ...DEFAULT_ASSET_OPTIONS.TEXTURE,
            ...options
        });
    }

    /**
     * 加载音频资源
     * @param {string} url - 音频文件URL
     * @param {Object} [options={}] - 加载选项
     * @returns {Promise<BABYLON.Sound>} 加载的音频资源
     */
    async loadSound(url, options = {}) {
        return this.loadAsset(url, ASSET_TYPES.SOUND, {
            ...DEFAULT_ASSET_OPTIONS.SOUND,
            ...options
        });
    }

    /**
     * 加载配置文件
     * @param {string} url - 配置文件URL
     * @param {Object} [options={}] - 加载选项
     * @returns {Promise<Object>} 解析的配置对象
     */
    async loadConfig(url, options = {}) {
        return this.loadAsset(url, ASSET_TYPES.CONFIG, {
            ...DEFAULT_ASSET_OPTIONS.CONFIG,
            ...options
        });
    }

    /**
     * 通用资源加载方法
     * @param {string} url - 资源URL
     * @param {string} type - 资源类型
     * @param {Object} [options={}] - 加载选项
     * @returns {Promise<any>} 加载的资源
     */
    async loadAsset(url, type, options = {}) {
        if (!this.isInitialized) {
            throw new Error('资源管理器未初始化');
        }

        if (this.isDestroyed) {
            throw new Error('资源管理器已销毁');
        }

        // 生成资源ID
        const assetId = options.id || this.generateAssetId(url, type);

        // 检查缓存
        const cachedAsset = this.cache.get(assetId);
        if (cachedAsset && !options.forceReload) {
            this.stats.cacheHits++;
            this.emit('cacheHit', { id: assetId, url, type });
            return cachedAsset.data;
        }

        this.stats.cacheMisses++;

        // 检查是否正在加载
        if (this.loadingTasks.has(assetId)) {
            return this.loadingTasks.get(assetId);
        }

        // 创建加载任务
        const loadTask = this.createLoadTask(url, type, assetId, options);
        this.loadingTasks.set(assetId, loadTask);

        try {
            const result = await loadTask;
            return result;
        } finally {
            this.loadingTasks.delete(assetId);
        }
    }

    /**
     * 创建加载任务
     * @param {string} url - 资源URL
     * @param {string} type - 资源类型
     * @param {string} assetId - 资源ID
     * @param {Object} options - 加载选项
     * @returns {Promise<any>} 加载任务Promise
     */
    async createLoadTask(url, type, assetId, options) {
        const startTime = performance.now();

        try {
            // 使用加载器加载资源
            const asset = await this.loader.load(url, type, options);

            // 计算加载时间
            const loadTime = performance.now() - startTime;
            this.updateLoadTimeStats(loadTime);

            // 缓存资源
            if (options.cache !== false) {
                this.cache.set(assetId, {
                    data: asset,
                    url,
                    type,
                    size: this.estimateAssetSize(asset, type),
                    loadTime,
                    createdAt: Date.now()
                });
            }

            this.stats.totalLoaded++;
            this.emit('loadComplete', { id: assetId, url, type, asset, loadTime });

            return asset;
        } catch (error) {
            this.stats.totalFailed++;
            this.emit('loadError', { id: assetId, url, type, error });
            throw error;
        }
    }

    /**
     * 生成资源ID
     * @param {string} url - 资源URL
     * @param {string} type - 资源类型
     * @returns {string} 资源ID
     */
    generateAssetId(url, type) {
        return `${type}_${url.replace(/[^a-zA-Z0-9]/g, '_')}`;
    }

    /**
     * 估算资源大小
     * @param {any} asset - 资源对象
     * @param {string} type - 资源类型
     * @returns {number} 估算的字节大小
     */
    estimateAssetSize(asset, type) {
        // 简单的大小估算逻辑
        switch (type) {
            case ASSET_TYPES.TEXTURE:
                if (asset && asset.getSize) {
                    const size = asset.getSize();
                    return size.width * size.height * 4; // RGBA
                }
                return 1024 * 1024; // 默认1MB

            case ASSET_TYPES.MODEL:
                return 5 * 1024 * 1024; // 默认5MB

            case ASSET_TYPES.SOUND:
                return 2 * 1024 * 1024; // 默认2MB

            case ASSET_TYPES.CONFIG:
                return JSON.stringify(asset).length;

            default:
                return 1024; // 默认1KB
        }
    }

    /**
     * 更新加载时间统计
     * @param {number} loadTime - 加载时间（毫秒）
     */
    updateLoadTimeStats(loadTime) {
        this.stats.loadTimes.push(loadTime);

        // 保持最近100次加载的记录
        if (this.stats.loadTimes.length > 100) {
            this.stats.loadTimes.shift();
        }

        // 计算平均加载时间
        this.stats.averageLoadTime = this.stats.loadTimes.reduce((sum, time) => sum + time, 0) / this.stats.loadTimes.length;
    }

    /**
     * 预加载资源列表
     * @param {Array<Object>} assetList - 资源列表
     * @param {Function} [onProgress] - 进度回调
     * @returns {Promise<Array>} 预加载结果
     */
    async preloadAssets(assetList, onProgress = null) {
        if (!Array.isArray(assetList) || assetList.length === 0) {
            return [];
        }

        const results = [];
        const total = assetList.length;
        let completed = 0;

        // 分批预加载
        const batchSize = this.options.preloadBatchSize;
        for (let i = 0; i < assetList.length; i += batchSize) {
            const batch = assetList.slice(i, i + batchSize);

            const batchPromises = batch.map(async (assetInfo) => {
                try {
                    const { url, type, options = {} } = assetInfo;
                    const asset = await this.loadAsset(url, type, { ...options, cache: true });

                    completed++;
                    if (onProgress) {
                        onProgress({ completed, total, current: assetInfo });
                    }

                    return { success: true, asset, url, type };
                } catch (error) {
                    completed++;
                    if (onProgress) {
                        onProgress({ completed, total, current: assetInfo, error });
                    }

                    return { success: false, error, url: assetInfo.url, type: assetInfo.type };
                }
            });

            const batchResults = await Promise.all(batchPromises);
            results.push(...batchResults);
        }

        this.emit('preloadComplete', { results, total, succeeded: results.filter(r => r.success).length });

        return results;
    }

    /**
     * 获取资源
     * @param {string} id - 资源ID
     * @returns {any|null} 资源对象或null
     */
    getAsset(id) {
        const cachedAsset = this.cache.get(id);
        return cachedAsset ? cachedAsset.data : null;
    }

    /**
     * 释放资源
     * @param {string} id - 资源ID
     * @returns {boolean} 是否成功释放
     */
    releaseAsset(id) {
        const asset = this.cache.get(id);
        if (!asset) {
            return false;
        }

        // 调用资源的dispose方法（如果存在）
        if (asset.data && typeof asset.data.dispose === 'function') {
            try {
                asset.data.dispose();
            } catch (error) {
                console.warn(`释放资源时出错: ${id}`, error);
            }
        }

        // 从缓存中移除
        this.cache.delete(id);

        this.emit('assetReleased', { id, asset });

        return true;
    }

    /**
     * 获取加载进度
     * @returns {Object} 加载进度信息
     */
    getLoadingProgress() {
        const activeLoads = this.loadingTasks.size;
        const queuedLoads = this.loadingQueue.length + this.preloadQueue.length;

        return {
            activeLoads,
            queuedLoads,
            totalPending: activeLoads + queuedLoads,
            isLoading: activeLoads > 0 || queuedLoads > 0
        };
    }

    /**
     * 获取性能统计
     * @returns {Object} 性能统计信息
     */
    getPerformanceStats() {
        return {
            ...this.stats,
            cacheStats: this.cache.getStats(),
            memoryUsage: this.getMemoryUsage()
        };
    }

    /**
     * 获取内存使用情况
     * @returns {Object} 内存使用信息
     */
    getMemoryUsage() {
        return {
            totalSize: this.stats.totalSize,
            cacheSize: this.cache.getCurrentSize(),
            maxCacheSize: this.options.maxCacheSize,
            utilization: (this.cache.getCurrentSize() / this.options.maxCacheSize) * 100
        };
    }

    /**
     * 清理未使用的资源
     * @param {number} [maxAge] - 最大年龄（毫秒）
     * @returns {number} 清理的资源数量
     */
    cleanup(maxAge = 300000) { // 默认5分钟
        const now = Date.now();
        let cleanedCount = 0;

        for (const [id, asset] of this.cache.entries()) {
            if (now - asset.createdAt > maxAge) {
                if (this.releaseAsset(id)) {
                    cleanedCount++;
                }
            }
        }

        this.emit('cleanup', { cleanedCount, maxAge });

        return cleanedCount;
    }

    /**
     * 启动预加载处理器
     */
    startPreloadProcessor() {
        if (this.preloadProcessor) {
            return;
        }

        this.preloadProcessor = setInterval(() => {
            if (this.preloadQueue.length > 0 && this.loadingTasks.size < this.options.maxConcurrentLoads) {
                const assetInfo = this.preloadQueue.shift();
                this.loadAsset(assetInfo.url, assetInfo.type, assetInfo.options).catch(error => {
                    console.warn('预加载失败:', assetInfo.url, error);
                });
            }
        }, 100);
    }

    /**
     * 启动性能监控
     */
    startPerformanceMonitoring() {
        if (this.performanceMonitor) {
            return;
        }

        this.performanceMonitor = setInterval(() => {
            const stats = this.getPerformanceStats();
            this.emit('performanceUpdate', stats);

            // 内存压力检查
            if (stats.memoryUsage.utilization > 90) {
                this.emit('memoryPressure', stats.memoryUsage);
                this.cleanup(60000); // 清理1分钟以上的资源
            }
        }, 5000); // 每5秒检查一次
    }

    /**
     * 处理加载完成事件
     * @param {Object} data - 加载完成数据
     */
    handleLoadComplete(data) {
        this.emit('loadComplete', data);
    }

    /**
     * 处理加载错误事件
     * @param {Object} data - 加载错误数据
     */
    handleLoadError(data) {
        this.emit('loadError', data);
    }

    /**
     * 销毁资源管理器
     */
    dispose() {
        if (this.isDestroyed) {
            return;
        }

        // 停止定时器
        if (this.preloadProcessor) {
            clearInterval(this.preloadProcessor);
            this.preloadProcessor = null;
        }

        if (this.performanceMonitor) {
            clearInterval(this.performanceMonitor);
            this.performanceMonitor = null;
        }

        // 清理所有资源
        this.cache.clear();

        // 取消所有加载任务
        this.loadingTasks.clear();
        this.loadingQueue.length = 0;
        this.preloadQueue.length = 0;

        // 销毁加载器
        if (this.loader) {
            this.loader.dispose();
        }

        // 销毁缓存
        if (this.cache) {
            this.cache.dispose();
        }

        this.isDestroyed = true;
        this.emit('disposed', { manager: this });

        console.log('资源管理器已销毁');
    }
}
