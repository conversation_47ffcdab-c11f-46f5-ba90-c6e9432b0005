// src/ecs/integration-example.js
// ECS系统与现有SceneManager的集成示例

import { setupECS, ENTITY_PRESETS, COMPONENT_TYPES } from './index.js';
import { Vector3, Color3 } from '@babylonjs/core';

/**
 * ECS集成示例类
 * 展示如何将ECS系统集成到现有的场景管理器中
 */
export class ECSIntegrationExample {
    constructor(sceneManager) {
        this.sceneManager = sceneManager;
        this.ecs = null;
        this.entities = new Map();

        console.log('ECS集成示例已创建');
    }

    /**
     * 初始化ECS系统
     */
    initializeECS() {
        // 获取当前活动场景
        const currentScene = this.sceneManager.getCurrentScene();
        const scene = currentScene?.babylonScene;

        if (!scene) {
            console.error('无法获取活动场景，ECS初始化失败');
            return false;
        }

        // 设置ECS系统
        this.ecs = setupECS(scene, {
            manager: {
                maxEntities: 1000,
                enablePerformanceMonitoring: true
            },
            render: {
                enableLOD: true,
                enableFrustumCulling: true,
                enableBatching: false
            },
            physics: {
                gravity: { x: 0, y: -9.81, z: 0 },
                enableDebugDraw: false
            }
        });

        // 启动ECS系统
        this.ecs.start();

        // 监听场景切换事件
        this.sceneManager.on('sceneChanged', (data) => {
            this.handleSceneChange(data);
        });

        console.log('ECS系统已初始化');
        return true;
    }

    /**
     * 处理场景切换
     */
    handleSceneChange(data) {
        console.log(`场景切换: ${data.from} -> ${data.to}`);

        // 清理当前ECS系统
        if (this.ecs) {
            this.ecs.cleanup();
        }

        // 为新场景重新初始化ECS
        this.initializeECS();

        // 根据新场景类型创建相应的实体
        this.createSceneEntities(data.to);
    }

    /**
     * 根据场景类型创建实体
     */
    createSceneEntities(sceneType) {
        switch (sceneType) {
            case 'game':
                this.createGameEntities();
                break;
            case 'menu':
                this.createMenuEntities();
                break;
            case 'loading':
                this.createLoadingEntities();
                break;
            default:
                console.log(`未知场景类型: ${sceneType}`);
        }
    }

    /**
     * 创建游戏场景实体
     */
    createGameEntities() {
        console.log('创建游戏场景实体...');

        // 创建玩家
        const player = this.ecs.createEntity('player')
            .fromPreset(ENTITY_PRESETS.PLAYER)
            .at(0, 1, 0)
            .withColor('#0066ff')
            .asLocalOwned('local_player')
            .withTags('player', 'controllable')
            .withMetadata('health', 100)
            .withMetadata('level', 1)
            .build();

        this.entities.set('player', player);

        // 创建一些NPC
        for (let i = 0; i < 3; i++) {
            const npc = this.ecs.createEntity(`npc_${i}`)
                .fromPreset(ENTITY_PRESETS.NPC)
                .at(Math.random() * 10 - 5, 1, Math.random() * 10 - 5)
                .withColor(new Color3(Math.random(), Math.random(), Math.random()))
                .withTags('npc', 'ai')
                .withMetadata('dialogue', `Hello from NPC ${i}`)
                .build();

            this.entities.set(`npc_${i}`, npc);
        }

        // 创建环境物体
        this.createEnvironmentObjects();

        // 创建收集品
        this.createCollectibles();

        console.log(`游戏场景实体创建完成，共 ${this.entities.size} 个实体`);
    }

    /**
     * 创建环境物体
     */
    createEnvironmentObjects() {
        // 创建地面
        const ground = this.ecs.createEntity('ground')
            .fromPreset(ENTITY_PRESETS.STATIC)
            .at(0, 0, 0)
            .scaledBy(20, 0.1, 20)
            .withMesh('ground', { width: 20, height: 20 })
            .withColor('#228B22')
            .withTags('environment', 'ground')
            .build();

        this.entities.set('ground', ground);

        // 创建一些墙壁
        const walls = [
            { pos: [10, 2, 0], scale: [0.5, 4, 20] },
            { pos: [-10, 2, 0], scale: [0.5, 4, 20] },
            { pos: [0, 2, 10], scale: [20, 4, 0.5] },
            { pos: [0, 2, -10], scale: [20, 4, 0.5] }
        ];

        walls.forEach((wallData, index) => {
            const wall = this.ecs.createEntity(`wall_${index}`)
                .fromPreset(ENTITY_PRESETS.STATIC)
                .at(...wallData.pos)
                .scaledBy(...wallData.scale)
                .withColor('#8B4513')
                .withTags('environment', 'wall')
                .build();

            this.entities.set(`wall_${index}`, wall);
        });

        // 创建一些装饰物体
        for (let i = 0; i < 5; i++) {
            const decoration = this.ecs.createEntity(`decoration_${i}`)
                .fromPreset(ENTITY_PRESETS.DECORATION)
                .at(
                    Math.random() * 16 - 8,
                    Math.random() * 2 + 1,
                    Math.random() * 16 - 8
                )
                .withMesh('sphere')
                .withColor(new Color3(Math.random(), Math.random(), Math.random()))
                .withTags('environment', 'decoration')
                .build();

            this.entities.set(`decoration_${i}`, decoration);
        }
    }

    /**
     * 创建收集品
     */
    createCollectibles() {
        for (let i = 0; i < 10; i++) {
            const collectible = this.ecs.createEntity(`collectible_${i}`)
                .at(
                    Math.random() * 16 - 8,
                    1,
                    Math.random() * 16 - 8
                )
                .withRender({
                    meshType: 'sphere',
                    diffuseColor: '#FFD700'
                })
                .withPhysics({
                    type: 'sphere',
                    mass: 0,
                    isTrigger: true
                })
                .withAnimation({
                    autoPlay: 'rotate'
                })
                .withTags('collectible', 'item')
                .withMetadata('value', Math.floor(Math.random() * 100) + 10)
                .build();

            // 创建旋转动画
            const animationComponent = collectible.getComponent('AnimationComponent');
            if (animationComponent) {
                animationComponent.createTransformAnimation(
                    'rotate',
                    'rotation',
                    [
                        { frame: 0, value: new Vector3(0, 0, 0) },
                        { frame: 60, value: new Vector3(0, Math.PI * 2, 0) }
                    ],
                    { frameRate: 30, loop: true }
                );
            }

            this.entities.set(`collectible_${i}`, collectible);
        }
    }

    /**
     * 创建菜单场景实体
     */
    createMenuEntities() {
        console.log('创建菜单场景实体...');

        // 菜单场景通常不需要复杂的ECS实体
        // 这里创建一些装饰性的实体

        const logo = this.ecs.createEntity('logo')
            .fromPreset(ENTITY_PRESETS.DECORATION)
            .at(0, 2, -5)
            .withMesh('sphere')
            .withColor('#FF6B6B')
            .withAnimation({ autoPlay: 'float' })
            .withTags('ui', 'decoration')
            .build();

        this.entities.set('logo', logo);
    }

    /**
     * 创建加载场景实体
     */
    createLoadingEntities() {
        console.log('创建加载场景实体...');

        // 创建加载指示器
        const loadingIndicator = this.ecs.createEntity('loading_indicator')
            .fromPreset(ENTITY_PRESETS.DECORATION)
            .at(0, 0, -3)
            .withMesh('sphere')
            .withColor('#4ECDC4')
            .withAnimation({ autoPlay: 'spin' })
            .withTags('ui', 'loading')
            .build();

        this.entities.set('loading_indicator', loadingIndicator);
    }

    /**
     * 更新ECS系统
     * 应该在主游戏循环中调用
     */
    update() {
        if (this.ecs) {
            this.ecs.update();
        }
    }

    /**
     * 获取实体
     */
    getEntity(name) {
        return this.entities.get(name);
    }

    /**
     * 获取所有实体
     */
    getAllEntities() {
        return Array.from(this.entities.values());
    }

    /**
     * 根据标签查找实体
     */
    getEntitiesByTag(tag) {
        return this.getAllEntities().filter(entity => entity.hasTag(tag));
    }

    /**
     * 根据组件类型查找实体
     */
    getEntitiesByComponent(componentType) {
        if (this.ecs) {
            return this.ecs.manager.queryEntities(componentType);
        }
        return [];
    }

    /**
     * 添加碰撞检测示例
     */
    setupCollisionHandling() {
        const physicsSystem = this.ecs?.systems.physics;
        if (!physicsSystem) return;

        // 添加碰撞回调
        physicsSystem.addCollisionCallback('collectible_pickup', (collisionInfo) => {
            const { entity1, entity2 } = collisionInfo;

            // 检查是否是玩家与收集品的碰撞
            const player = entity1.hasTag('player') ? entity1 :
                          entity2.hasTag('player') ? entity2 : null;
            const collectible = entity1.hasTag('collectible') ? entity1 :
                               entity2.hasTag('collectible') ? entity2 : null;

            if (player && collectible) {
                this.handleCollectiblePickup(player, collectible);
            }
        });
    }

    /**
     * 处理收集品拾取
     */
    handleCollectiblePickup(player, collectible) {
        const value = collectible.getMetadata('value') || 0;

        console.log(`玩家拾取了价值 ${value} 的收集品`);

        // 播放拾取效果
        const animationComponent = collectible.getComponent('AnimationComponent');
        if (animationComponent) {
            animationComponent.play('pickup', {
                onAnimationEnd: () => {
                    collectible.destroy();
                    this.entities.delete(collectible.name);
                }
            });
        } else {
            collectible.destroy();
            this.entities.delete(collectible.name);
        }

        // 更新玩家分数
        const currentScore = player.getMetadata('score') || 0;
        player.setMetadata('score', currentScore + value);

        // 触发事件
        this.sceneManager.emit('collectiblePickup', {
            player,
            collectible,
            value,
            newScore: currentScore + value
        });
    }

    /**
     * 获取性能统计
     */
    getPerformanceStats() {
        if (!this.ecs) return null;

        return {
            ecs: this.ecs.manager.getPerformanceStats(),
            render: this.ecs.systems.render?.getRenderStats(),
            physics: this.ecs.systems.physics?.getPhysicsStats()
        };
    }

    /**
     * 清理资源
     */
    cleanup() {
        console.log('清理ECS集成示例...');

        if (this.ecs) {
            this.ecs.cleanup();
            this.ecs = null;
        }

        this.entities.clear();

        console.log('ECS集成示例清理完成');
    }
}

/**
 * 在SceneManager中集成ECS的便捷函数
 */
export function integrateECSWithSceneManager(sceneManager) {
    const integration = new ECSIntegrationExample(sceneManager);

    // 监听场景切换完成事件，在场景准备好后初始化ECS
    sceneManager.on('sceneTransitionComplete', (data) => {
        console.log('场景切换完成，初始化ECS系统...');
        const success = integration.initializeECS();
        if (success) {
            console.log('ECS系统初始化成功');
        }
    });

    // 将ECS更新添加到场景管理器的更新循环中
    const originalUpdate = sceneManager.update.bind(sceneManager);
    sceneManager.update = function(deltaTime) {
        // 调用原始更新方法
        originalUpdate(deltaTime);

        // 更新ECS系统
        integration.update();
    };

    // 将ECS集成实例添加到场景管理器
    sceneManager.ecs = integration;

    console.log('ECS已成功集成到SceneManager');

    return integration;
}

export default ECSIntegrationExample;
