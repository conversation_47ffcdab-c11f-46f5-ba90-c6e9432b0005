// src/assets/AssetLoader.js
// 资源加载器 - 负责具体的资源加载逻辑

import { EventEmitter } from '../utils/eventEmitter.js';
import { ASSET_TYPES } from './AssetTypes.js';

// Babylon.js导入
import { SceneLoader } from '@babylonjs/core/Loading/sceneLoader';
import { Texture } from '@babylonjs/core/Materials/Textures/texture';
import { Sound } from '@babylonjs/core/Audio/sound';
import { HDRCubeTexture } from '@babylonjs/core/Materials/Textures/hdrCubeTexture';
import { CubeTexture } from '@babylonjs/core/Materials/Textures/cubeTexture';

// 导入加载器扩展
import '@babylonjs/loaders/glTF';
import '@babylonjs/loaders/OBJ';

/**
 * 资源加载器 - 处理各种类型资源的具体加载逻辑
 */
export class AssetLoader extends EventEmitter {
    /**
     * 构造函数
     * @param {BABYLON.Scene} scene - Babylon.js场景实例
     * @param {Object} [options={}] - 配置选项
     */
    constructor(scene, options = {}) {
        super();
        
        // Babylon.js场景引用
        this.scene = scene;
        
        // 配置选项
        this.options = {
            maxConcurrentLoads: 6,
            retryAttempts: 3,
            retryDelay: 1000,
            enableDebugLogging: false,
            timeout: 30000, // 30秒超时
            ...options
        };
        
        // 当前加载任务计数
        this.activeLoads = 0;
        
        // 加载队列
        this.loadQueue = [];
        
        // 状态标记
        this.isInitialized = false;
        this.isDestroyed = false;
        
        console.log('资源加载器已创建');
    }
    
    /**
     * 初始化加载器
     */
    async initialize() {
        if (this.isInitialized) {
            return;
        }
        
        try {
            // 设置Babylon.js加载器配置
            this.setupBabylonLoaders();
            
            this.isInitialized = true;
            console.log('资源加载器初始化完成');
        } catch (error) {
            console.error('资源加载器初始化失败:', error);
            throw error;
        }
    }
    
    /**
     * 设置Babylon.js加载器配置
     */
    setupBabylonLoaders() {
        // 设置glTF加载器选项
        if (SceneLoader.ImportMeshAsync) {
            SceneLoader.OnPluginActivatedObservable.add((plugin) => {
                if (plugin.name === 'gltf') {
                    // 启用glTF扩展
                    plugin.loader.extensionsUsed = plugin.loader.extensionsUsed || [];
                    plugin.loader.extensionsRequired = plugin.loader.extensionsRequired || [];
                }
            });
        }
    }
    
    /**
     * 加载资源
     * @param {string} url - 资源URL
     * @param {string} type - 资源类型
     * @param {Object} [options={}] - 加载选项
     * @returns {Promise<any>} 加载的资源
     */
    async load(url, type, options = {}) {
        if (!this.isInitialized) {
            throw new Error('资源加载器未初始化');
        }
        
        if (this.isDestroyed) {
            throw new Error('资源加载器已销毁');
        }
        
        // 等待加载槽位
        await this.waitForLoadSlot();
        
        this.activeLoads++;
        
        try {
            this.emit('loadStart', { url, type, options });
            
            const asset = await this.loadWithRetry(url, type, options);
            
            this.emit('loadComplete', { url, type, asset, options });
            
            return asset;
        } catch (error) {
            this.emit('loadError', { url, type, error, options });
            throw error;
        } finally {
            this.activeLoads--;
            this.processQueue();
        }
    }
    
    /**
     * 等待加载槽位
     */
    async waitForLoadSlot() {
        while (this.activeLoads >= this.options.maxConcurrentLoads) {
            await new Promise(resolve => setTimeout(resolve, 10));
        }
    }
    
    /**
     * 带重试的加载
     * @param {string} url - 资源URL
     * @param {string} type - 资源类型
     * @param {Object} options - 加载选项
     * @returns {Promise<any>} 加载的资源
     */
    async loadWithRetry(url, type, options) {
        let lastError;
        
        for (let attempt = 0; attempt <= this.options.retryAttempts; attempt++) {
            try {
                if (attempt > 0) {
                    console.log(`重试加载资源 (${attempt}/${this.options.retryAttempts}): ${url}`);
                    await new Promise(resolve => setTimeout(resolve, this.options.retryDelay * attempt));
                }
                
                return await this.loadByType(url, type, options);
            } catch (error) {
                lastError = error;
                
                if (this.options.enableDebugLogging) {
                    console.warn(`加载失败 (尝试 ${attempt + 1}): ${url}`, error);
                }
                
                // 某些错误不需要重试
                if (this.isNonRetryableError(error)) {
                    break;
                }
            }
        }
        
        throw lastError;
    }
    
    /**
     * 根据类型加载资源
     * @param {string} url - 资源URL
     * @param {string} type - 资源类型
     * @param {Object} options - 加载选项
     * @returns {Promise<any>} 加载的资源
     */
    async loadByType(url, type, options) {
        // 创建超时Promise
        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error(`加载超时: ${url}`)), this.options.timeout);
        });
        
        let loadPromise;
        
        switch (type) {
            case ASSET_TYPES.MODEL:
                loadPromise = this.loadModel(url, options);
                break;
            
            case ASSET_TYPES.TEXTURE:
                loadPromise = this.loadTexture(url, options);
                break;
            
            case ASSET_TYPES.SOUND:
                loadPromise = this.loadSound(url, options);
                break;
            
            case ASSET_TYPES.CONFIG:
                loadPromise = this.loadConfig(url, options);
                break;
            
            case ASSET_TYPES.ANIMATION:
                loadPromise = this.loadAnimation(url, options);
                break;
            
            default:
                throw new Error(`不支持的资源类型: ${type}`);
        }
        
        return Promise.race([loadPromise, timeoutPromise]);
    }
    
    /**
     * 加载3D模型
     * @param {string} url - 模型URL
     * @param {Object} options - 加载选项
     * @returns {Promise<Object>} 模型数据
     */
    async loadModel(url, options) {
        try {
            const result = await SceneLoader.ImportMeshAsync(
                options.meshNames || '',
                this.getBaseUrl(url),
                this.getFileName(url),
                this.scene,
                (progress) => {
                    this.emit('loadProgress', {
                        url,
                        type: ASSET_TYPES.MODEL,
                        progress: progress.loaded / progress.total
                    });
                }
            );
            
            // 应用变换
            if (options.scaleFactor && options.scaleFactor !== 1.0) {
                result.meshes.forEach(mesh => {
                    if (mesh.scaling) {
                        mesh.scaling.scaleInPlace(options.scaleFactor);
                    }
                });
            }
            
            if (options.positionOffset) {
                const offset = options.positionOffset;
                result.meshes.forEach(mesh => {
                    if (mesh.position) {
                        mesh.position.addInPlace(new BABYLON.Vector3(offset.x, offset.y, offset.z));
                    }
                });
            }
            
            return {
                meshes: result.meshes,
                particleSystems: result.particleSystems,
                skeletons: result.skeletons,
                animationGroups: result.animationGroups,
                transformNodes: result.transformNodes,
                geometries: result.geometries,
                lights: result.lights
            };
        } catch (error) {
            throw new Error(`模型加载失败: ${url} - ${error.message}`);
        }
    }
    
    /**
     * 加载纹理
     * @param {string} url - 纹理URL
     * @param {Object} options - 加载选项
     * @returns {Promise<BABYLON.Texture>} 纹理对象
     */
    async loadTexture(url, options) {
        try {
            // 检查是否是HDR纹理
            if (url.toLowerCase().endsWith('.hdr') || url.toLowerCase().endsWith('.exr')) {
                return new HDRCubeTexture(url, this.scene, 512, false, true, false, true);
            }
            
            // 检查是否是立方体纹理
            if (options.isCubeTexture) {
                return new CubeTexture(url, this.scene);
            }
            
            // 普通纹理
            const texture = new Texture(url, this.scene, !options.generateMipMaps, options.invertY);
            
            // 设置纹理属性
            if (options.wrapU !== undefined) texture.wrapU = options.wrapU;
            if (options.wrapV !== undefined) texture.wrapV = options.wrapV;
            if (options.samplingMode !== undefined) texture.updateSamplingMode(options.samplingMode);
            
            // 等待纹理加载完成
            await new Promise((resolve, reject) => {
                texture.onLoadObservable.addOnce(() => resolve());
                texture.onErrorObservable.addOnce(() => reject(new Error('纹理加载失败')));
            });
            
            return texture;
        } catch (error) {
            throw new Error(`纹理加载失败: ${url} - ${error.message}`);
        }
    }
    
    /**
     * 加载音频
     * @param {string} url - 音频URL
     * @param {Object} options - 加载选项
     * @returns {Promise<BABYLON.Sound>} 音频对象
     */
    async loadSound(url, options) {
        try {
            return new Promise((resolve, reject) => {
                const sound = new Sound(
                    options.name || this.getFileName(url),
                    url,
                    this.scene,
                    () => resolve(sound), // onReady
                    {
                        loop: options.loop || false,
                        autoplay: options.autoplay || false,
                        volume: options.volume || 1.0,
                        playbackRate: options.playbackRate || 1.0,
                        spatialSound: options.spatialSound || false,
                        maxDistance: options.maxDistance || 100,
                        rolloffFactor: options.rolloffFactor || 1,
                        refDistance: options.refDistance || 1,
                        distanceModel: options.distanceModel || 'linear',
                        streaming: options.streaming || false
                    },
                    (error) => reject(new Error(`音频加载失败: ${url} - ${error}`))
                );
            });
        } catch (error) {
            throw new Error(`音频加载失败: ${url} - ${error.message}`);
        }
    }
    
    /**
     * 加载配置文件
     * @param {string} url - 配置文件URL
     * @param {Object} options - 加载选项
     * @returns {Promise<Object>} 配置对象
     */
    async loadConfig(url, options) {
        try {
            const response = await fetch(url);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const text = await response.text();
            
            if (options.parseJSON !== false) {
                try {
                    return JSON.parse(text);
                } catch (parseError) {
                    throw new Error(`JSON解析失败: ${parseError.message}`);
                }
            }
            
            return text;
        } catch (error) {
            throw new Error(`配置文件加载失败: ${url} - ${error.message}`);
        }
    }
    
    /**
     * 加载动画
     * @param {string} url - 动画文件URL
     * @param {Object} options - 加载选项
     * @returns {Promise<Array>} 动画组数组
     */
    async loadAnimation(url, options) {
        try {
            const result = await SceneLoader.ImportAnimationsAsync(
                this.getBaseUrl(url),
                this.getFileName(url),
                this.scene,
                false,
                options.animationGroupLoadingMode
            );
            
            return result;
        } catch (error) {
            throw new Error(`动画加载失败: ${url} - ${error.message}`);
        }
    }
    
    /**
     * 获取基础URL
     * @param {string} url - 完整URL
     * @returns {string} 基础URL
     */
    getBaseUrl(url) {
        const lastSlashIndex = url.lastIndexOf('/');
        return lastSlashIndex !== -1 ? url.substring(0, lastSlashIndex + 1) : '';
    }
    
    /**
     * 获取文件名
     * @param {string} url - 完整URL
     * @returns {string} 文件名
     */
    getFileName(url) {
        const lastSlashIndex = url.lastIndexOf('/');
        return lastSlashIndex !== -1 ? url.substring(lastSlashIndex + 1) : url;
    }
    
    /**
     * 判断是否为不可重试的错误
     * @param {Error} error - 错误对象
     * @returns {boolean} 是否不可重试
     */
    isNonRetryableError(error) {
        const message = error.message.toLowerCase();
        return message.includes('404') || 
               message.includes('not found') || 
               message.includes('unauthorized') ||
               message.includes('forbidden');
    }
    
    /**
     * 处理加载队列
     */
    processQueue() {
        // 队列处理逻辑可以在这里实现
        // 目前使用简单的并发控制
    }
    
    /**
     * 销毁加载器
     */
    dispose() {
        if (this.isDestroyed) {
            return;
        }
        
        // 清空队列
        this.loadQueue.length = 0;
        
        this.isDestroyed = true;
        console.log('资源加载器已销毁');
    }
}
