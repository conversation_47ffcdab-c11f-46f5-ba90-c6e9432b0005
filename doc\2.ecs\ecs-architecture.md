# ECS架构设计文档

## 概述

本文档描述了《Cantos》项目中实体组件系统（Entity-Component-System, ECS）的架构设计。ECS是一种软件架构模式，广泛应用于游戏开发中，通过将数据（组件）与行为（系统）分离，提供了高度的灵活性和可维护性。

## 设计理念

### 核心原则

1. **组合优于继承**：通过组合不同的组件来定义实体的行为，而不是使用复杂的继承层次结构
2. **数据与逻辑分离**：组件存储数据，系统处理逻辑
3. **高性能**：通过缓存友好的数据布局和批处理优化性能
4. **可扩展性**：易于添加新的组件和系统
5. **可测试性**：每个组件和系统都可以独立测试

### 架构优势

- **灵活性**：可以通过添加/移除组件来动态改变实体的行为
- **可重用性**：组件和系统可以在不同的实体类型之间重用
- **可维护性**：清晰的职责分离使代码更易于理解和维护
- **性能**：系统可以批量处理具有相同组件的实体
- **并行性**：不同的系统可以并行运行（在适当的情况下）

## 核心架构

### 实体（Entity）

实体是游戏世界中的基本对象，本身不包含任何数据或逻辑，只是组件的容器。

```javascript
class Entity {
    constructor(id, name)
    addComponent(component)
    removeComponent(componentType)
    getComponent(componentType)
    hasComponent(componentType)
    destroy()
}
```

**特性：**
- 唯一标识符（ID）
- 可读名称
- 组件集合
- 标签系统（用于快速分类）
- 元数据存储
- 父子关系支持
- 生命周期管理

### 组件（Component）

组件是纯数据容器，定义了实体的属性和状态。

```javascript
class Component {
    constructor(entity, options)
    onAttached()
    onDetached()
    update(deltaTime)
    serialize()
    deserialize(data)
}
```

**特性：**
- 数据存储
- 生命周期钩子
- 序列化支持
- 依赖管理
- 启用/禁用状态
- 元数据支持

### 系统（System）

系统包含游戏逻辑，处理具有特定组件组合的实体。

```javascript
class System {
    constructor(scene, options)
    update(deltaTime, entities)
    addEntity(entity)
    removeEntity(entity)
    matchesEntity(entity)
}
```

**特性：**
- 实体过滤
- 批量处理
- 优先级控制
- 性能统计
- 启用/禁用状态
- 更新频率控制

### ECS管理器（ECSManager）

ECS管理器协调所有实体、组件和系统的运行。

```javascript
class ECSManager {
    constructor(scene, options)
    createEntity(name)
    addSystem(system)
    registerComponent(ComponentClass)
    update()
    queryEntities(...componentTypes)
}
```

**特性：**
- 实体生命周期管理
- 系统调度
- 组件注册
- 实体查询
- 性能监控
- 事件系统

## 预定义组件

### TransformComponent（变换组件）

管理实体在3D空间中的位置、旋转和缩放。

```javascript
class TransformComponent extends Component {
    position: Vector3
    rotation: Quaternion
    scale: Vector3
    
    setPosition(x, y, z)
    setRotation(quaternion)
    setScale(x, y, z)
    getWorldMatrix()
    lookAt(target)
}
```

**功能：**
- 3D变换管理
- 世界/本地坐标转换
- 父子关系支持
- 约束系统
- 变换缓存

### RenderComponent（渲染组件）

管理实体的视觉表现，包括网格、材质、纹理等。

```javascript
class RenderComponent extends Component {
    mesh: BABYLON.Mesh
    material: BABYLON.Material
    
    setVisible(visible)
    setColor(colorType, color)
    setTexture(textureType, textureUrl)
    getBoundingInfo()
}
```

**功能：**
- 网格管理
- 材质系统
- 纹理支持
- LOD（细节层次）
- 可见性控制
- 渲染统计

### PhysicsComponent（物理组件）

管理实体的物理属性，集成Havok物理引擎。

```javascript
class PhysicsComponent extends Component {
    physicsAggregate: PhysicsAggregate
    
    applyForce(force, point)
    applyImpulse(impulse, point)
    setLinearVelocity(velocity)
    setAngularVelocity(angularVelocity)
}
```

**功能：**
- 刚体物理
- 碰撞检测
- 力和冲量
- 物理材质
- 约束系统
- 触发器支持

### AnimationComponent（动画组件）

管理实体的动画播放和控制。

```javascript
class AnimationComponent extends Component {
    play(name, options)
    stop(name)
    pause(name)
    setSpeed(name, speed)
    createAnimation(name, property, keyframes)
}
```

**功能：**
- 动画播放控制
- 动画混合
- 关键帧动画
- 动画事件
- 动画队列
- 循环和速度控制

### NetworkComponent（网络组件）

管理实体的网络同步和通信。

```javascript
class NetworkComponent extends Component {
    networkId: string
    isLocalOwned: boolean
    
    setCustomData(key, value)
    setNetworkOwnership(isOwned, ownerId)
}
```

**功能：**
- 网络同步
- 客户端预测
- 插值补偿
- 压缩传输
- 所有权管理
- 自定义数据同步

## 核心系统

### RenderSystem（渲染系统）

处理所有渲染相关的逻辑。

**功能：**
- 视锥体剔除
- LOD管理
- 批处理优化
- 渲染统计
- 材质管理

### PhysicsSystem（物理系统）

处理物理模拟和碰撞检测。

**功能：**
- 物理步进
- 碰撞处理
- 约束求解
- 休眠管理
- 调试绘制

## 工具和辅助类

### ComponentFactory（组件工厂）

提供便捷的组件创建方法。

```javascript
const factory = new ComponentFactory();
factory.createFromPreset('player', entity);
factory.createPlayerComponents(entity, options);
```

### EntityBuilder（实体构建器）

提供流畅的实体创建API。

```javascript
const entity = builder
    .create('player')
    .at(0, 0, 0)
    .withRender({ meshType: 'capsule' })
    .withPhysics({ mass: 70 })
    .withAnimation({ autoPlay: 'idle' })
    .build();
```

### ComponentRegistry（组件注册表）

管理所有组件类型的注册和元数据。

```javascript
componentRegistry.register(MyComponent, {
    description: '自定义组件',
    dependencies: ['TransformComponent'],
    group: 'custom'
});
```

## 性能优化

### 内存管理

- **对象池**：重用频繁创建/销毁的对象
- **组件缓存**：缓存组件查询结果
- **批量操作**：批量处理实体操作

### 更新优化

- **系统优先级**：控制系统更新顺序
- **更新频率**：不同系统可以有不同的更新频率
- **实体过滤**：只处理需要的实体
- **脏标记**：只更新发生变化的数据

### 渲染优化

- **视锥体剔除**：只渲染可见的实体
- **LOD系统**：根据距离调整细节级别
- **批处理**：合并相似的渲染调用
- **实例化**：对相同网格使用实例化渲染

## 扩展性设计

### 自定义组件

```javascript
class HealthComponent extends Component {
    constructor(entity, options = {}) {
        super(entity, options);
        this.maxHealth = options.maxHealth || 100;
        this.currentHealth = this.maxHealth;
    }
    
    takeDamage(amount) {
        this.currentHealth = Math.max(0, this.currentHealth - amount);
        if (this.currentHealth === 0) {
            this.emit('death', { entity: this.entity });
        }
    }
}
```

### 自定义系统

```javascript
class HealthSystem extends System {
    constructor(scene, options = {}) {
        super(scene, options);
        this.requiredComponents = ['HealthComponent'];
    }
    
    updateEntity(entity, deltaTime) {
        const health = entity.getComponent('HealthComponent');
        // 处理生命值相关逻辑
    }
}
```

## 最佳实践

### 组件设计

1. **保持简单**：每个组件应该只负责一个明确的职责
2. **避免依赖**：尽量减少组件之间的依赖关系
3. **数据驱动**：组件应该主要包含数据，而不是逻辑
4. **序列化友好**：确保组件可以被序列化和反序列化

### 系统设计

1. **单一职责**：每个系统应该只处理一种类型的逻辑
2. **无状态**：系统应该是无状态的，所有状态都存储在组件中
3. **批量处理**：尽可能批量处理实体以提高性能
4. **错误处理**：妥善处理异常情况，避免影响其他系统

### 实体设计

1. **组合优于继承**：使用组件组合而不是继承来定义实体类型
2. **预设模板**：为常见的实体类型创建预设模板
3. **生命周期管理**：正确管理实体的创建和销毁
4. **标签和元数据**：合理使用标签和元数据来组织实体

## 调试和监控

### 性能监控

- 系统更新时间统计
- 实体数量监控
- 内存使用情况
- 渲染统计信息

### 调试工具

- 实体检查器
- 组件编辑器
- 系统性能分析器
- 可视化调试器

### 日志系统

- 分级日志记录
- 组件生命周期日志
- 系统执行日志
- 错误和警告追踪

## 总结

ECS架构为《Cantos》项目提供了一个强大、灵活且高性能的游戏对象管理系统。通过合理的设计和实现，它能够支持复杂的游戏逻辑，同时保持代码的可维护性和可扩展性。

这个架构的成功实施需要团队成员理解ECS的核心概念，并遵循最佳实践来设计和实现组件和系统。随着项目的发展，这个架构将为添加新功能和优化性能提供坚实的基础。
