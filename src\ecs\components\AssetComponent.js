// src/ecs/components/AssetComponent.js
// 资源组件 - 用于实体关联和管理资源

import { Component } from '../Component.js';
import { ASSET_TYPES, ASSET_STATUS } from '../../assets/AssetTypes.js';

/**
 * 资源组件 - 管理实体相关的资源
 * 支持多种资源类型的加载、缓存和释放
 */
export class AssetComponent extends Component {
    /**
     * 构造函数
     * @param {Entity} entity - 所属实体
     * @param {Object} [options={}] - 组件选项
     */
    constructor(entity, options = {}) {
        super(entity, options);
        
        // 资源管理器引用（将在系统中设置）
        this.assetManager = null;
        
        // 资源配置
        this.assetConfigs = new Map(); // 资源配置映射
        this.loadedAssets = new Map();  // 已加载的资源映射
        this.loadingPromises = new Map(); // 正在加载的Promise映射
        
        // 组件状态
        this.isLoading = false;
        this.loadProgress = 0;
        this.loadErrors = [];
        
        // 资源类型计数
        this.assetCounts = {
            [ASSET_TYPES.MODEL]: 0,
            [ASSET_TYPES.TEXTURE]: 0,
            [ASSET_TYPES.SOUND]: 0,
            [ASSET_TYPES.CONFIG]: 0,
            [ASSET_TYPES.ANIMATION]: 0,
            [ASSET_TYPES.MATERIAL]: 0
        };
        
        // 自动释放配置
        this.autoRelease = options.autoRelease !== false; // 默认启用
        this.releaseOnDetach = options.releaseOnDetach !== false; // 默认启用
        
        console.log(`资源组件已创建: ${this.entity?.id}`);
    }
    
    /**
     * 组件初始化
     */
    initialize() {
        super.initialize();
        
        // 从选项中加载初始资源
        if (this.options.assets) {
            this.loadAssetsFromConfig(this.options.assets);
        }
    }
    
    /**
     * 设置资源管理器
     * @param {AssetManager} assetManager - 资源管理器实例
     */
    setAssetManager(assetManager) {
        this.assetManager = assetManager;
    }
    
    /**
     * 添加资源配置
     * @param {string} name - 资源名称
     * @param {string} url - 资源URL
     * @param {string} type - 资源类型
     * @param {Object} [options={}] - 加载选项
     */
    addAsset(name, url, type, options = {}) {
        const config = {
            name,
            url,
            type,
            options: { ...options },
            status: ASSET_STATUS.PENDING,
            addedAt: Date.now()
        };
        
        this.assetConfigs.set(name, config);
        this.assetCounts[type] = (this.assetCounts[type] || 0) + 1;
        
        this.emit('assetAdded', { name, config });
        
        console.log(`资源配置已添加: ${name} (${type})`);
    }
    
    /**
     * 移除资源配置
     * @param {string} name - 资源名称
     * @returns {boolean} 是否成功移除
     */
    removeAsset(name) {
        const config = this.assetConfigs.get(name);
        if (!config) {
            return false;
        }
        
        // 释放已加载的资源
        if (this.loadedAssets.has(name)) {
            this.releaseAsset(name);
        }
        
        // 取消正在加载的任务
        if (this.loadingPromises.has(name)) {
            this.loadingPromises.delete(name);
        }
        
        // 移除配置
        this.assetConfigs.delete(name);
        this.assetCounts[config.type] = Math.max(0, (this.assetCounts[config.type] || 1) - 1);
        
        this.emit('assetRemoved', { name, config });
        
        console.log(`资源配置已移除: ${name}`);
        return true;
    }
    
    /**
     * 加载单个资源
     * @param {string} name - 资源名称
     * @returns {Promise<any>} 加载的资源
     */
    async loadAsset(name) {
        if (!this.assetManager) {
            throw new Error('资源管理器未设置');
        }
        
        const config = this.assetConfigs.get(name);
        if (!config) {
            throw new Error(`资源配置不存在: ${name}`);
        }
        
        // 检查是否已加载
        if (this.loadedAssets.has(name)) {
            return this.loadedAssets.get(name);
        }
        
        // 检查是否正在加载
        if (this.loadingPromises.has(name)) {
            return this.loadingPromises.get(name);
        }
        
        // 开始加载
        config.status = ASSET_STATUS.LOADING;
        
        const loadPromise = this.assetManager.loadAsset(config.url, config.type, {
            ...config.options,
            id: `${this.entity.id}_${name}`
        });
        
        this.loadingPromises.set(name, loadPromise);
        
        try {
            const asset = await loadPromise;
            
            // 保存加载的资源
            this.loadedAssets.set(name, asset);
            config.status = ASSET_STATUS.LOADED;
            config.loadedAt = Date.now();
            
            this.emit('assetLoaded', { name, asset, config });
            
            console.log(`资源加载完成: ${name}`);
            return asset;
        } catch (error) {
            config.status = ASSET_STATUS.ERROR;
            config.error = error;
            
            this.loadErrors.push({ name, error, timestamp: Date.now() });
            
            this.emit('assetLoadError', { name, error, config });
            
            console.error(`资源加载失败: ${name}`, error);
            throw error;
        } finally {
            this.loadingPromises.delete(name);
        }
    }
    
    /**
     * 加载所有资源
     * @param {Function} [onProgress] - 进度回调
     * @returns {Promise<Map>} 加载结果映射
     */
    async loadAllAssets(onProgress = null) {
        if (!this.assetManager) {
            throw new Error('资源管理器未设置');
        }
        
        const assetNames = Array.from(this.assetConfigs.keys());
        if (assetNames.length === 0) {
            return new Map();
        }
        
        this.isLoading = true;
        this.loadProgress = 0;
        this.loadErrors = [];
        
        const results = new Map();
        let completed = 0;
        
        try {
            // 并行加载所有资源
            const loadPromises = assetNames.map(async (name) => {
                try {
                    const asset = await this.loadAsset(name);
                    results.set(name, { success: true, asset });
                    
                    completed++;
                    this.loadProgress = completed / assetNames.length;
                    
                    if (onProgress) {
                        onProgress({
                            name,
                            completed,
                            total: assetNames.length,
                            progress: this.loadProgress
                        });
                    }
                    
                    return { name, success: true, asset };
                } catch (error) {
                    results.set(name, { success: false, error });
                    
                    completed++;
                    this.loadProgress = completed / assetNames.length;
                    
                    if (onProgress) {
                        onProgress({
                            name,
                            completed,
                            total: assetNames.length,
                            progress: this.loadProgress,
                            error
                        });
                    }
                    
                    return { name, success: false, error };
                }
            });
            
            await Promise.all(loadPromises);
            
            this.emit('allAssetsLoaded', { results, errors: this.loadErrors });
            
            console.log(`所有资源加载完成，成功: ${results.size - this.loadErrors.length}，失败: ${this.loadErrors.length}`);
            
            return results;
        } finally {
            this.isLoading = false;
        }
    }
    
    /**
     * 获取资源
     * @param {string} name - 资源名称
     * @returns {any|null} 资源对象或null
     */
    getAsset(name) {
        return this.loadedAssets.get(name) || null;
    }
    
    /**
     * 检查资源是否已加载
     * @param {string} name - 资源名称
     * @returns {boolean} 是否已加载
     */
    hasAsset(name) {
        return this.loadedAssets.has(name);
    }
    
    /**
     * 获取资源状态
     * @param {string} name - 资源名称
     * @returns {string|null} 资源状态或null
     */
    getAssetStatus(name) {
        const config = this.assetConfigs.get(name);
        return config ? config.status : null;
    }
    
    /**
     * 释放单个资源
     * @param {string} name - 资源名称
     * @returns {boolean} 是否成功释放
     */
    releaseAsset(name) {
        const asset = this.loadedAssets.get(name);
        if (!asset) {
            return false;
        }
        
        // 调用资源的dispose方法（如果存在）
        if (typeof asset.dispose === 'function') {
            try {
                asset.dispose();
            } catch (error) {
                console.warn(`释放资源时出错: ${name}`, error);
            }
        }
        
        // 从已加载资源中移除
        this.loadedAssets.delete(name);
        
        // 更新配置状态
        const config = this.assetConfigs.get(name);
        if (config) {
            config.status = ASSET_STATUS.DISPOSED;
        }
        
        this.emit('assetReleased', { name, asset });
        
        console.log(`资源已释放: ${name}`);
        return true;
    }
    
    /**
     * 释放所有资源
     */
    releaseAllAssets() {
        const assetNames = Array.from(this.loadedAssets.keys());
        let releasedCount = 0;
        
        for (const name of assetNames) {
            if (this.releaseAsset(name)) {
                releasedCount++;
            }
        }
        
        this.emit('allAssetsReleased', { releasedCount });
        
        console.log(`所有资源已释放，共 ${releasedCount} 个`);
    }
    
    /**
     * 从配置加载资源
     * @param {Array|Object} assetsConfig - 资源配置
     */
    loadAssetsFromConfig(assetsConfig) {
        if (Array.isArray(assetsConfig)) {
            // 数组格式
            assetsConfig.forEach((config, index) => {
                const name = config.name || `asset_${index}`;
                this.addAsset(name, config.url, config.type, config.options);
            });
        } else if (typeof assetsConfig === 'object') {
            // 对象格式
            Object.entries(assetsConfig).forEach(([name, config]) => {
                this.addAsset(name, config.url, config.type, config.options);
            });
        }
    }
    
    /**
     * 获取组件调试信息
     * @returns {Object} 调试信息
     */
    getDebugInfo() {
        return {
            ...super.getDebugInfo(),
            assetCount: this.assetConfigs.size,
            loadedCount: this.loadedAssets.size,
            loadingCount: this.loadingPromises.size,
            isLoading: this.isLoading,
            loadProgress: this.loadProgress,
            errorCount: this.loadErrors.length,
            assetCounts: { ...this.assetCounts }
        };
    }
    
    /**
     * 组件分离时调用
     */
    onDetached() {
        if (this.releaseOnDetach) {
            this.releaseAllAssets();
        }
        
        super.onDetached();
    }
    
    /**
     * 组件销毁
     */
    dispose() {
        if (this.autoRelease) {
            this.releaseAllAssets();
        }
        
        // 清理数据
        this.assetConfigs.clear();
        this.loadedAssets.clear();
        this.loadingPromises.clear();
        this.loadErrors = [];
        
        super.dispose();
        
        console.log(`资源组件已销毁: ${this.entity?.id}`);
    }
}
