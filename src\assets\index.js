// src/assets/index.js
// 资源管理系统统一导出文件

// 核心类
export { AssetManager } from './AssetManager.js';
export { AssetLoader } from './AssetLoader.js';
export { AssetCache } from './AssetCache.js';

// 类型定义和配置
export {
    ASSET_TYPES,
    ASSET_EXTENSIONS,
    DEFAULT_ASSET_OPTIONS,
    ASSET_PRIORITY,
    ASSET_STATUS,
    CACHE_STRATEGY,
    ASSET_PRESETS,
    getAssetTypeFromUrl,
    isAssetTypeSupported,
    getDefaultOptionsForType,
    isExtensionSupported,
    AssetConfigValidator
} from './AssetTypes.js';

// ECS组件和系统 (从ecs模块重新导出)
export { AssetComponent } from '../ecs/components/AssetComponent.js';
export { AssetSystem } from '../ecs/systems/AssetSystem.js';

/**
 * 创建资源管理器实例
 * @param {BABYLON.Scene} scene - Babylon.js场景
 * @param {Object} [options={}] - 配置选项
 * @returns {AssetManager} 资源管理器实例
 */
export function createAssetManager(scene, options = {}) {
    return new AssetManager(scene, options);
}

/**
 * 资源管理系统默认配置
 */
export const ASSET_MANAGER_DEFAULT_CONFIG = {
    // 缓存配置
    maxCacheSize: 500 * 1024 * 1024, // 500MB
    enableAutoCleanup: true,
    cleanupInterval: 60000, // 60秒
    
    // 加载配置
    maxConcurrentLoads: 6,
    retryAttempts: 3,
    retryDelay: 1000,
    
    // 预加载配置
    enablePreloading: true,
    preloadBatchSize: 5,
    
    // 调试配置
    enableDebugLogging: false,
    enablePerformanceMonitoring: true
};

/**
 * 资源系统默认配置
 */
export const ASSET_SYSTEM_DEFAULT_CONFIG = {
    // 自动加载配置
    autoLoadOnAdd: true,
    autoLoadDelay: 100,
    
    // 批处理配置
    enableBatchLoading: true,
    batchSize: 5,
    batchInterval: 200,
    
    // 性能配置
    maxConcurrentLoads: 3,
    enablePreloading: true,
    
    // 清理配置
    enableAutoCleanup: true,
    cleanupInterval: 30000,
    unusedThreshold: 60000
};

/**
 * 快速设置资源管理系统
 * @param {BABYLON.Scene} scene - Babylon.js场景
 * @param {Object} [options={}] - 配置选项
 * @returns {Object} 资源管理系统对象
 */
export function setupAssetManagement(scene, options = {}) {
    const config = {
        manager: { ...ASSET_MANAGER_DEFAULT_CONFIG, ...options.manager },
        system: { ...ASSET_SYSTEM_DEFAULT_CONFIG, ...options.system }
    };
    
    // 创建资源管理器
    const assetManager = new AssetManager(scene, config.manager);
    
    // 创建资源系统
    const assetSystem = new AssetSystem(scene, config.system);
    
    // 设置系统的资源管理器引用
    assetSystem.assetManager = assetManager;
    
    return {
        manager: assetManager,
        system: assetSystem,
        
        // 便捷方法
        async initialize() {
            await assetManager.initialize();
            assetSystem.initialize();
        },
        
        dispose() {
            assetSystem.dispose();
            assetManager.dispose();
        },
        
        // 快速加载方法
        async loadTexture(url, options) {
            return assetManager.loadTexture(url, options);
        },
        
        async loadModel(url, options) {
            return assetManager.loadModel(url, options);
        },
        
        async loadSound(url, options) {
            return assetManager.loadSound(url, options);
        },
        
        async loadConfig(url, options) {
            return assetManager.loadConfig(url, options);
        },
        
        async preloadAssets(assetList, onProgress) {
            return assetManager.preloadAssets(assetList, onProgress);
        },
        
        // 统计信息
        getStats() {
            return {
                manager: assetManager.getPerformanceStats(),
                system: assetSystem.getPerformanceStats()
            };
        }
    };
}

/**
 * 资源管理工具函数
 */
export const AssetUtils = {
    /**
     * 验证资源URL
     * @param {string} url - 资源URL
     * @returns {boolean} 是否有效
     */
    isValidUrl(url) {
        try {
            new URL(url);
            return true;
        } catch {
            return false;
        }
    },
    
    /**
     * 获取文件扩展名
     * @param {string} url - 文件URL
     * @returns {string} 扩展名
     */
    getFileExtension(url) {
        return url.toLowerCase().substring(url.lastIndexOf('.'));
    },
    
    /**
     * 获取文件名（不含扩展名）
     * @param {string} url - 文件URL
     * @returns {string} 文件名
     */
    getFileName(url) {
        const fileName = url.substring(url.lastIndexOf('/') + 1);
        const dotIndex = fileName.lastIndexOf('.');
        return dotIndex !== -1 ? fileName.substring(0, dotIndex) : fileName;
    },
    
    /**
     * 格式化文件大小
     * @param {number} bytes - 字节数
     * @returns {string} 格式化的大小字符串
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    /**
     * 生成资源ID
     * @param {string} url - 资源URL
     * @param {string} type - 资源类型
     * @param {string} [prefix=''] - 前缀
     * @returns {string} 资源ID
     */
    generateAssetId(url, type, prefix = '') {
        const cleanUrl = url.replace(/[^a-zA-Z0-9]/g, '_');
        return prefix ? `${prefix}_${type}_${cleanUrl}` : `${type}_${cleanUrl}`;
    },
    
    /**
     * 创建资源配置对象
     * @param {string} url - 资源URL
     * @param {string} [type] - 资源类型（自动推断）
     * @param {Object} [options={}] - 选项
     * @returns {Object} 资源配置
     */
    createAssetConfig(url, type = null, options = {}) {
        const inferredType = type || getAssetTypeFromUrl(url);
        
        if (!inferredType) {
            throw new Error(`无法确定资源类型: ${url}`);
        }
        
        return {
            url,
            type: inferredType,
            options: {
                ...getDefaultOptionsForType(inferredType),
                ...options
            }
        };
    },
    
    /**
     * 批量创建资源配置
     * @param {Array<string|Object>} assets - 资源列表
     * @returns {Array<Object>} 资源配置列表
     */
    createAssetConfigs(assets) {
        return assets.map((asset, index) => {
            if (typeof asset === 'string') {
                return this.createAssetConfig(asset);
            } else if (typeof asset === 'object' && asset.url) {
                return this.createAssetConfig(asset.url, asset.type, asset.options);
            } else {
                throw new Error(`无效的资源配置: ${index}`);
            }
        });
    }
};

/**
 * 资源管理系统信息
 */
export const ASSET_SYSTEM_INFO = {
    name: '资源管理系统',
    version: '1.0.0',
    description: '《山海经》MMORPG客户端资源管理系统',
    author: 'Cantos Development Team',
    features: [
        '异步资源加载',
        'LRU缓存管理',
        '智能预加载',
        '内存压力管理',
        '性能监控',
        'ECS集成',
        '错误恢复',
        '批处理优化'
    ],
    supportedTypes: Object.values(ASSET_TYPES)
};
