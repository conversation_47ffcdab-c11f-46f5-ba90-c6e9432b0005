// src/assets/example.js
// 资源管理系统使用示例

import { setupAssetManagement, ASSET_TYPES } from './index.js';

/**
 * 资源管理系统示例
 * 演示如何使用资源管理系统加载和管理游戏资源
 */
export class AssetManagementExample {
    constructor(scene) {
        this.scene = scene;
        this.assetManagement = null;
    }
    
    /**
     * 初始化资源管理系统
     */
    async initialize() {
        console.log('🚀 初始化资源管理系统示例...');
        
        // 设置资源管理系统
        this.assetManagement = setupAssetManagement(this.scene, {
            manager: {
                maxCacheSize: 100 * 1024 * 1024, // 100MB缓存
                maxConcurrentLoads: 3,
                enablePreloading: true,
                enableDebugLogging: true
            },
            system: {
                autoLoadOnAdd: true,
                enableBatchLoading: true,
                enableAutoCleanup: true
            }
        });
        
        // 初始化
        await this.assetManagement.initialize();
        
        // 监听事件
        this.setupEventListeners();
        
        console.log('✅ 资源管理系统初始化完成');
    }
    
    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        const { manager } = this.assetManagement;
        
        manager.on('loadStart', (data) => {
            console.log(`📥 开始加载: ${data.url}`);
        });
        
        manager.on('loadComplete', (data) => {
            console.log(`✅ 加载完成: ${data.url}`);
        });
        
        manager.on('loadError', (data) => {
            console.error(`❌ 加载失败: ${data.url}`, data.error);
        });
        
        manager.on('cacheHit', (data) => {
            console.log(`🎯 缓存命中: ${data.id}`);
        });
        
        manager.on('memoryPressure', (data) => {
            console.warn('⚠️ 内存压力警告', data);
        });
    }
    
    /**
     * 演示基础资源加载
     */
    async demonstrateBasicLoading() {
        console.log('\n📦 演示基础资源加载...');
        
        try {
            // 加载配置文件（模拟）
            const config = await this.loadMockConfig();
            console.log('配置加载完成:', config);
            
            // 加载纹理（模拟）
            const texture = await this.loadMockTexture();
            console.log('纹理加载完成:', texture);
            
            // 加载音频（模拟）
            const sound = await this.loadMockSound();
            console.log('音频加载完成:', sound);
            
        } catch (error) {
            console.error('基础加载演示失败:', error);
        }
    }
    
    /**
     * 演示预加载功能
     */
    async demonstratePreloading() {
        console.log('\n🔄 演示预加载功能...');
        
        const assetList = [
            { url: 'mock://texture1.png', type: ASSET_TYPES.TEXTURE },
            { url: 'mock://texture2.png', type: ASSET_TYPES.TEXTURE },
            { url: 'mock://sound1.mp3', type: ASSET_TYPES.SOUND },
            { url: 'mock://config.json', type: ASSET_TYPES.CONFIG }
        ];
        
        try {
            const results = await this.assetManagement.preloadAssets(assetList, (progress) => {
                console.log(`预加载进度: ${progress.completed}/${progress.total} (${(progress.progress * 100).toFixed(1)}%)`);
            });
            
            const successCount = results.filter(r => r.success).length;
            console.log(`预加载完成: ${successCount}/${results.length} 成功`);
            
        } catch (error) {
            console.error('预加载演示失败:', error);
        }
    }
    
    /**
     * 演示性能监控
     */
    demonstratePerformanceMonitoring() {
        console.log('\n📊 演示性能监控...');
        
        const stats = this.assetManagement.getStats();
        
        console.log('资源管理器统计:', {
            总加载数: stats.manager.totalLoaded,
            失败数: stats.manager.totalFailed,
            缓存命中数: stats.manager.cacheHits,
            缓存未命中数: stats.manager.cacheMisses,
            平均加载时间: `${stats.manager.averageLoadTime.toFixed(2)}ms`,
            内存使用: `${(stats.manager.memoryUsage.totalSize / 1024 / 1024).toFixed(1)}MB`
        });
        
        if (stats.system) {
            console.log('资源系统统计:', {
                总实体数: stats.system.assetStats.totalEntities,
                加载中实体数: stats.system.assetStats.loadingEntities,
                已加载实体数: stats.system.assetStats.loadedEntities,
                失败实体数: stats.system.assetStats.failedEntities
            });
        }
    }
    
    /**
     * 演示内存管理
     */
    demonstrateMemoryManagement() {
        console.log('\n🧹 演示内存管理...');
        
        const { manager } = this.assetManagement;
        
        // 获取内存使用情况
        const memoryUsage = manager.getMemoryUsage();
        console.log('内存使用情况:', {
            总大小: `${(memoryUsage.totalSize / 1024 / 1024).toFixed(1)}MB`,
            缓存大小: `${(memoryUsage.cacheSize / 1024 / 1024).toFixed(1)}MB`,
            最大缓存: `${(memoryUsage.maxCacheSize / 1024 / 1024).toFixed(1)}MB`,
            使用率: `${memoryUsage.utilization.toFixed(1)}%`
        });
        
        // 手动清理资源
        const cleanedCount = manager.cleanup(0); // 立即清理所有资源
        console.log(`清理了 ${cleanedCount} 个资源`);
    }
    
    /**
     * 运行完整演示
     */
    async runDemo() {
        try {
            await this.initialize();
            await this.demonstrateBasicLoading();
            await this.demonstratePreloading();
            this.demonstratePerformanceMonitoring();
            this.demonstrateMemoryManagement();
            
            console.log('\n🎉 资源管理系统演示完成！');
            
        } catch (error) {
            console.error('演示运行失败:', error);
        }
    }
    
    // 模拟加载方法
    async loadMockConfig() {
        return this.assetManagement.manager.loadAsset('mock://config.json', ASSET_TYPES.CONFIG, {
            parseJSON: false // 返回原始文本
        });
    }
    
    async loadMockTexture() {
        return this.assetManagement.loadTexture('mock://texture.png');
    }
    
    async loadMockSound() {
        return this.assetManagement.loadSound('mock://sound.mp3');
    }
    
    /**
     * 销毁示例
     */
    dispose() {
        if (this.assetManagement) {
            this.assetManagement.dispose();
            this.assetManagement = null;
        }
        
        console.log('🗑️ 资源管理系统示例已销毁');
    }
}

/**
 * 创建并运行资源管理系统演示
 * @param {BABYLON.Scene} scene - Babylon.js场景
 */
export async function runAssetManagementDemo(scene) {
    const example = new AssetManagementExample(scene);
    await example.runDemo();
    return example;
}

// 如果直接运行此文件，则执行演示
if (typeof window !== 'undefined') {
    window.AssetManagementExample = AssetManagementExample;
    window.runAssetManagementDemo = runAssetManagementDemo;
}
