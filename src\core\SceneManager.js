// src/core/SceneManager.js
// 场景管理系统 - 管理Babylon.js场景生命周期，处理场景切换和资源清理

import { EventEmitter } from '../utils/eventEmitter.js';

/**
 * 场景管理系统类
 * 负责管理所有场景的生命周期，包括创建、切换、销毁等操作
 */
export class SceneManager extends EventEmitter {
    constructor(engine) {
        super();

        // Babylon.js引擎实例
        this.engine = engine;

        // 当前活动场景
        this.currentScene = null;
        this.currentSceneId = null;

        // 场景存储映射 - 存储已创建的场景实例
        this.scenes = new Map();

        // 场景类型工厂映射 - 存储场景类型和对应的工厂函数, type -> factory, factory是一个返回场景实例的函数
        this.sceneFactories = new Map();

        // 预加载的场景映射
        this.preloadedScenes = new Map();

        // 场景切换状态
        this.isTransitioning = false;
        this.transitionPromise = null;

        // 性能监控
        this.performanceStats = {
            sceneCreationTime: 0,
            lastSwitchTime: 0,
            memoryUsage: 0
        };

        console.log("场景管理系统已初始化");
    }

    /**
     * 注册场景类型工厂, 其目的是准备好场景创建的函数
     * @param {string} type - 场景类型标识符
     * @param {Function} factory - 场景工厂函数，返回场景实例
     */
    registerSceneType(type, factory) {
        if (typeof factory !== 'function') {
            throw new Error(`场景工厂必须是函数: ${type}`);
        }

        this.sceneFactories.set(type, factory);
        console.log(`场景类型已注册: ${type}`);

        // 触发场景类型注册事件
        this.emit('sceneTypeRegistered', { type, factory });
    }

    /**
     * 创建场景
     * @param {Object} sceneConfig - 场景配置对象
     * @param {string} sceneConfig.id - 场景唯一标识符
     * @param {string} sceneConfig.type - 场景类型
     * @param {Object} sceneConfig.options - 场景选项
     * @param {HTMLCanvasElement} canvas - 画布元素
     * @returns {Promise<Object>} 创建的场景实例
     */
    async createScene(sceneConfig, canvas) {
        const { id, type, options = {} } = sceneConfig; // sceneConfig是SceneFactory.js中DefaultSceneConfigs对应的一个value

        if (!id || !type) {
            throw new Error("场景配置必须包含id和type字段");
        }

        if (this.scenes.has(id)) {
            console.warn(`场景 ${id} 已存在，返回现有实例`);
            return this.scenes.get(id);
        }

        // 检查场景类型是否已注册
        const factory = this.sceneFactories.get(type);
        if (!factory) {
            throw new Error(`未注册的场景类型: ${type}`);
        }

        console.log(`开始创建场景: ${id} (类型: ${type})`);
        const startTime = performance.now();

        try {
            // 触发场景创建开始事件
            this.emit('sceneCreationStart', { id, type, options });

            // 使用工厂函数创建场景实例
            const sceneInstance = factory(options);

            // 调用场景实例的createScene方法
            const babylonScene = await sceneInstance.createScene(this.engine, canvas);

            if (!babylonScene) {
                throw new Error(`场景创建失败: ${id}`);
            }

            // 存储场景信息
            const sceneData = {
                id,
                type,
                options,
                instance: sceneInstance,
                babylonScene,
                createdAt: Date.now(),
                isActive: false
            };

            this.scenes.set(id, sceneData);

            // 记录性能统计
            const creationTime = performance.now() - startTime;
            this.performanceStats.sceneCreationTime = creationTime;

            console.log(`场景创建完成: ${id} (耗时: ${creationTime.toFixed(2)}ms)`);

            // 触发场景创建完成事件
            this.emit('sceneCreated', {
                id,
                type,
                sceneData,
                creationTime
            });

            return sceneData;

        } catch (error) {
            console.error(`场景创建失败: ${id}`, error);

            // 触发场景创建失败事件
            this.emit('sceneCreationError', { id, type, error });

            throw error;
        }
    }

    /**
     * 切换到指定场景
     * @param {string} sceneId - 目标场景ID
     * @param {Object} transitionOptions - 切换选项
     * @returns {Promise<Object>} 切换后的场景数据
     */
    async switchScene(sceneId, transitionOptions = {}) {
        // 防止并发切换
        if (this.isTransitioning) {
            console.warn("场景正在切换中，等待当前切换完成...");
            await this.transitionPromise;
        }

        const targetScene = this.scenes.get(sceneId);
        if (!targetScene) {
            throw new Error(`场景不存在: ${sceneId}`);
        }

        if (this.currentSceneId === sceneId) {
            console.log(`已经在目标场景中: ${sceneId}`);
            return targetScene;
        }

        console.log(`开始切换场景: ${this.currentSceneId} -> ${sceneId}`);
        const startTime = performance.now();

        this.isTransitioning = true;
        this.transitionPromise = this._performSceneSwitch(targetScene, transitionOptions);

        try {
            const result = await this.transitionPromise;

            // 记录切换时间
            this.performanceStats.lastSwitchTime = performance.now() - startTime;

            return result;

        } finally {
            this.isTransitioning = false;
            this.transitionPromise = null;
        }
    }

    /**
     * 执行场景切换的内部方法
     * @private
     */
    async _performSceneSwitch(targetScene, options) {
        const { enableTransition = true, fadeTime = 500 } = options;

        // 触发场景切换开始事件
        this.emit('sceneTransitionStart', {
            from: this.currentSceneId,
            to: targetScene.id,
            options
        });

        try {
            // 如果启用过渡效果
            if (enableTransition && this.currentScene) {
                await this._fadeOut(fadeTime / 2);
            }

            // 停用当前场景
            if (this.currentScene) {
                this.currentScene.isActive = false;

                // 如果场景实例有pause方法，调用它
                if (typeof this.currentScene.instance.pause === 'function') {
                    this.currentScene.instance.pause();
                }
            }

            // 激活目标场景
            this.currentScene = targetScene;
            this.currentSceneId = targetScene.id;
            targetScene.isActive = true;

            // 设置引擎渲染的场景
            this.engine.runRenderLoop(() => {
                targetScene.babylonScene.render();
            });

            // 如果场景实例有resume方法，调用它
            if (typeof targetScene.instance.resume === 'function') {
                targetScene.instance.resume();
            }

            // 如果启用过渡效果
            if (enableTransition) {
                await this._fadeIn(fadeTime / 2);
            }

            console.log(`场景切换完成: ${targetScene.id}`);

            // 触发场景切换完成事件
            this.emit('sceneTransitionComplete', {
                from: this.currentSceneId,
                to: targetScene.id,
                scene: targetScene
            });

            return targetScene;

        } catch (error) {
            console.error("场景切换失败:", error);

            // 触发场景切换失败事件
            this.emit('sceneTransitionError', {
                from: this.currentSceneId,
                to: targetScene.id,
                error
            });

            throw error;
        }
    }

    /**
     * 淡出效果
     * @private
     */
    async _fadeOut(duration) {
        // 这里可以实现淡出效果
        // 暂时使用简单的延时模拟
        return new Promise(resolve => setTimeout(resolve, duration));
    }

    /**
     * 淡入效果
     * @private
     */
    async _fadeIn(duration) {
        // 这里可以实现淡入效果
        // 暂时使用简单的延时模拟
        return new Promise(resolve => setTimeout(resolve, duration));
    }

    /**
     * 获取当前场景
     * @returns {Object|null} 当前场景数据
     */
    getCurrentScene() {
        return this.currentScene;
    }

    /**
     * 获取当前场景ID
     * @returns {string|null} 当前场景ID
     */
    getCurrentSceneId() {
        return this.currentSceneId;
    }

    /**
     * 预加载场景
     * @param {Object} sceneConfig - 场景配置
     * @param {HTMLCanvasElement} canvas - 画布元素
     */
    async preloadScene(sceneConfig, canvas) {
        const { id } = sceneConfig;

        if (this.preloadedScenes.has(id) || this.scenes.has(id)) {
            console.log(`场景 ${id} 已存在，跳过预加载`);
            return;
        }

        console.log(`开始预加载场景: ${id}`);

        try {
            const sceneData = await this.createScene(sceneConfig, canvas);
            this.preloadedScenes.set(id, sceneData);

            // 预加载的场景不激活
            sceneData.isActive = false;

            console.log(`场景预加载完成: ${id}`);

            // 触发预加载完成事件
            this.emit('scenePreloaded', { id, sceneData });

        } catch (error) {
            console.error(`场景预加载失败: ${id}`, error);
            throw error;
        }
    }

    /**
     * 销毁指定场景
     * @param {string} sceneId - 要销毁的场景ID
     */
    disposeScene(sceneId) {
        const sceneData = this.scenes.get(sceneId);
        if (!sceneData) {
            console.warn(`场景不存在，无法销毁: ${sceneId}`);
            return;
        }

        console.log(`开始销毁场景: ${sceneId}`);

        try {
            // 如果是当前活动场景，需要先切换到其他场景或清空
            if (this.currentSceneId === sceneId) {
                console.warn(`正在销毁当前活动场景: ${sceneId}`);
                this.currentScene = null;
                this.currentSceneId = null;
            }

            // 调用场景实例的dispose方法
            if (sceneData.instance && typeof sceneData.instance.dispose === 'function') {
                sceneData.instance.dispose();
            }

            // 销毁Babylon.js场景
            if (sceneData.babylonScene) {
                sceneData.babylonScene.dispose();
            }

            // 从映射中移除
            this.scenes.delete(sceneId);
            this.preloadedScenes.delete(sceneId);

            console.log(`场景销毁完成: ${sceneId}`);

            // 触发场景销毁事件
            this.emit('sceneDisposed', { id: sceneId });

        } catch (error) {
            console.error(`场景销毁失败: ${sceneId}`, error);

            // 触发场景销毁失败事件
            this.emit('sceneDisposeError', { id: sceneId, error });
        }
    }

    /**
     * 获取所有场景信息
     * @returns {Array} 场景信息数组
     */
    getAllScenes() {
        return Array.from(this.scenes.values()).map(sceneData => ({
            id: sceneData.id,
            type: sceneData.type,
            isActive: sceneData.isActive,
            createdAt: sceneData.createdAt
        }));
    }

    /**
     * 获取场景信息
     * @param {string} sceneId - 场景ID
     * @returns {Object|null} 场景信息
     */
    getSceneInfo(sceneId) {
        const sceneData = this.scenes.get(sceneId);
        if (!sceneData) {
            return null;
        }

        return {
            id: sceneData.id,
            type: sceneData.type,
            isActive: sceneData.isActive,
            createdAt: sceneData.createdAt,
            options: sceneData.options
        };
    }

    /**
     * 检查场景是否存在
     * @param {string} sceneId - 场景ID
     * @returns {boolean} 场景是否存在
     */
    hasScene(sceneId) {
        return this.scenes.has(sceneId);
    }

    /**
     * 获取性能统计信息
     * @returns {Object} 性能统计数据
     */
    getPerformanceStats() {
        return {
            ...this.performanceStats,
            totalScenes: this.scenes.size,
            preloadedScenes: this.preloadedScenes.size,
            currentSceneId: this.currentSceneId,
            memoryUsage: this._getMemoryUsage()
        };
    }

    /**
     * 获取内存使用情况（简化版本）
     * @private
     */
    _getMemoryUsage() {
        if (performance.memory) {
            return {
                used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
                total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
                limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
            };
        }
        return null;
    }

    /**
     * 清理所有场景
     */
    dispose() {
        console.log("开始清理场景管理系统...");

        // 销毁所有场景
        const sceneIds = Array.from(this.scenes.keys());
        sceneIds.forEach(sceneId => {
            this.disposeScene(sceneId);
        });

        // 清空映射
        this.scenes.clear();
        this.preloadedScenes.clear();
        this.sceneFactories.clear();

        // 重置状态
        this.currentScene = null;
        this.currentSceneId = null;
        this.isTransitioning = false;
        this.transitionPromise = null;

        // 清理事件监听器
        this.removeAllListeners();

        console.log("场景管理系统清理完成");
    }

    /**
     * 暂停当前场景
     */
    pauseCurrentScene() {
        if (this.currentScene && this.currentScene.instance) {
            if (typeof this.currentScene.instance.pause === 'function') {
                this.currentScene.instance.pause();
                console.log(`场景已暂停: ${this.currentSceneId}`);
            }
        }
    }

    /**
     * 恢复当前场景
     */
    resumeCurrentScene() {
        if (this.currentScene && this.currentScene.instance) {
            if (typeof this.currentScene.instance.resume === 'function') {
                this.currentScene.instance.resume();
                console.log(`场景已恢复: ${this.currentSceneId}`);
            }
        }
    }

    /**
     * 获取注册的场景类型列表
     * @returns {Array<string>} 场景类型数组
     */
    getRegisteredSceneTypes() {
        return Array.from(this.sceneFactories.keys());
    }

    /**
     * 更新场景管理器
     * @param {number} deltaTime - 时间增量（秒）
     */
    update(deltaTime) {
        // 更新当前活动场景
        if (this.currentScene && this.currentScene.instance) {
            // 如果场景实例有update方法，调用它
            if (typeof this.currentScene.instance.update === 'function') {
                this.currentScene.instance.update(deltaTime);
            }
        }
    }
}

export default SceneManager;
