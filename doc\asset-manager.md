# 资源管理系统文档

## 概述

资源管理系统 (AssetManager) 是《山海经》MMORPG客户端的核心模块之一，负责游戏中所有资源的加载、缓存、管理和释放。该系统采用现代化的异步加载机制，支持多种资源类型，并与ECS架构深度集成。

## 系统架构

### 核心组件

```
AssetManager (资源管理器)
├── AssetLoader (资源加载器)
├── AssetCache (资源缓存)
├── AssetTypes (资源类型定义)
└── ECS集成
    ├── AssetComponent (资源组件)
    └── AssetSystem (资源系统)
```

### 设计原则

- **异步优先**: 所有资源加载操作都是异步的，避免阻塞主线程
- **智能缓存**: 使用LRU算法管理内存，自动清理未使用的资源
- **类型安全**: 强类型的资源配置和验证机制
- **性能监控**: 内置性能统计和监控功能
- **错误恢复**: 完善的错误处理和重试机制

## 支持的资源类型

| 类型 | 扩展名 | 描述 | 用途 |
|------|--------|------|------|
| MODEL | .glb, .gltf, .babylon, .obj | 3D模型 | 角色、建筑、道具 |
| TEXTURE | .jpg, .png, .dds, .ktx, .hdr | 纹理贴图 | 材质、UI、环境 |
| SOUND | .mp3, .wav, .ogg | 音频文件 | 音效、背景音乐 |
| CONFIG | .json, .xml | 配置文件 | 游戏配置、数据表 |
| ANIMATION | .babylon, .glb | 动画数据 | 角色动画、特效 |
| MATERIAL | .json | 材质定义 | 自定义材质 |

## 快速开始

### 1. 基础使用

```javascript
import { AssetManager } from './src/assets/AssetManager.js';

// 创建资源管理器
const assetManager = new AssetManager(scene, {
    maxCacheSize: 500 * 1024 * 1024, // 500MB缓存
    maxConcurrentLoads: 6,           // 最大并发加载数
    enablePreloading: true           // 启用预加载
});

// 初始化
await assetManager.initialize();

// 加载资源
const texture = await assetManager.loadTexture('textures/character.png');
const model = await assetManager.loadModel('models/character.glb');
const sound = await assetManager.loadSound('sounds/footstep.mp3');
```

### 2. ECS集成使用

```javascript
import { AssetComponent } from './src/ecs/components/AssetComponent.js';

// 创建实体并添加资源组件
const entity = ecsManager.createEntity('player');
const assetComponent = new AssetComponent(entity, {
    autoRelease: true,
    releaseOnDetach: true
});

entity.addComponent(assetComponent);

// 添加资源配置
assetComponent.addAsset('playerModel', 'models/player.glb', ASSET_TYPES.MODEL);
assetComponent.addAsset('playerTexture', 'textures/player.png', ASSET_TYPES.TEXTURE);
assetComponent.addAsset('walkSound', 'sounds/walk.mp3', ASSET_TYPES.SOUND);

// 加载所有资源
await assetComponent.loadAllAssets((progress) => {
    console.log(`加载进度: ${(progress.progress * 100).toFixed(1)}%`);
});

// 获取已加载的资源
const playerModel = assetComponent.getAsset('playerModel');
const playerTexture = assetComponent.getAsset('playerTexture');
```

### 3. 预加载资源

```javascript
// 定义预加载资源列表
const preloadAssets = [
    { url: 'models/environment.glb', type: ASSET_TYPES.MODEL },
    { url: 'textures/skybox.hdr', type: ASSET_TYPES.TEXTURE },
    { url: 'sounds/ambient.mp3', type: ASSET_TYPES.SOUND },
    { url: 'config/game-settings.json', type: ASSET_TYPES.CONFIG }
];

// 预加载资源
const results = await assetManager.preloadAssets(preloadAssets, (progress) => {
    console.log(`预加载进度: ${progress.completed}/${progress.total}`);
});

// 检查预加载结果
const successCount = results.filter(r => r.success).length;
console.log(`预加载完成: ${successCount}/${results.length} 成功`);
```

## 高级功能

### 1. 资源配置和验证

```javascript
import { AssetConfigValidator, ASSET_PRESETS } from './src/assets/AssetTypes.js';

// 使用预设配置
const characterConfig = {
    ...ASSET_PRESETS.CHARACTER_MODEL,
    url: 'models/hero.glb',
    options: {
        scaleFactor: 1.2,
        importAnimations: true
    }
};

// 验证配置
const validation = AssetConfigValidator.validate(characterConfig);
if (!validation.isValid) {
    console.error('配置验证失败:', validation.errors);
}
```

### 2. 自定义加载选项

```javascript
// 纹理加载选项
const textureOptions = {
    generateMipMaps: true,
    samplingMode: 3, // TRILINEAR_SAMPLINGMODE
    wrapU: 1,        // WRAP_ADDRESSMODE
    wrapV: 1,
    invertY: true
};

const texture = await assetManager.loadTexture('texture.png', textureOptions);

// 模型加载选项
const modelOptions = {
    scaleFactor: 2.0,
    positionOffset: { x: 0, y: 1, z: 0 },
    importAnimations: true,
    mergeMeshes: false
};

const model = await assetManager.loadModel('model.glb', modelOptions);
```

### 3. 内存管理

```javascript
// 获取内存使用情况
const memoryUsage = assetManager.getMemoryUsage();
console.log(`缓存使用率: ${memoryUsage.utilization.toFixed(1)}%`);

// 手动清理资源
const cleanedCount = assetManager.cleanup(300000); // 清理5分钟以上的资源
console.log(`清理了 ${cleanedCount} 个资源`);

// 释放特定资源
const released = assetManager.releaseAsset('texture_id');
if (released) {
    console.log('资源已释放');
}
```

### 4. 性能监控

```javascript
// 获取性能统计
const stats = assetManager.getPerformanceStats();
console.log('性能统计:', {
    总加载数: stats.totalLoaded,
    失败数: stats.totalFailed,
    缓存命中率: `${(stats.cacheHits / (stats.cacheHits + stats.cacheMisses) * 100).toFixed(1)}%`,
    平均加载时间: `${stats.averageLoadTime.toFixed(2)}ms`,
    内存使用: `${(stats.memoryUsage.totalSize / 1024 / 1024).toFixed(1)}MB`
});

// 监听性能更新事件
assetManager.on('performanceUpdate', (stats) => {
    if (stats.memoryUsage.utilization > 90) {
        console.warn('内存使用率过高，建议清理资源');
    }
});
```

## 事件系统

### AssetManager 事件

```javascript
// 加载事件
assetManager.on('loadStart', (data) => {
    console.log(`开始加载: ${data.url}`);
});

assetManager.on('loadProgress', (data) => {
    console.log(`加载进度: ${data.url} - ${(data.progress * 100).toFixed(1)}%`);
});

assetManager.on('loadComplete', (data) => {
    console.log(`加载完成: ${data.url}`);
});

assetManager.on('loadError', (data) => {
    console.error(`加载失败: ${data.url}`, data.error);
});

// 缓存事件
assetManager.on('cacheHit', (data) => {
    console.log(`缓存命中: ${data.id}`);
});

assetManager.on('assetEvicted', (data) => {
    console.log(`资源被清理: ${data.key}`);
});

// 内存压力事件
assetManager.on('memoryPressure', (data) => {
    console.warn('内存压力警告', data);
});
```

### AssetComponent 事件

```javascript
// 组件级别事件
assetComponent.on('assetLoaded', (data) => {
    console.log(`资源加载完成: ${data.name}`);
});

assetComponent.on('allAssetsLoaded', (data) => {
    console.log('所有资源加载完成', data.results);
});

assetComponent.on('assetReleased', (data) => {
    console.log(`资源已释放: ${data.name}`);
});
```

## 最佳实践

### 1. 资源组织

```javascript
// 按功能分组资源
const playerAssets = {
    model: { url: 'models/player.glb', type: ASSET_TYPES.MODEL },
    texture: { url: 'textures/player.png', type: ASSET_TYPES.TEXTURE },
    animations: { url: 'animations/player.glb', type: ASSET_TYPES.ANIMATION }
};

const environmentAssets = {
    terrain: { url: 'models/terrain.glb', type: ASSET_TYPES.MODEL },
    skybox: { url: 'textures/skybox.hdr', type: ASSET_TYPES.TEXTURE },
    ambient: { url: 'sounds/ambient.mp3', type: ASSET_TYPES.SOUND }
};
```

### 2. 错误处理

```javascript
try {
    const texture = await assetManager.loadTexture('texture.png');
    // 使用纹理
} catch (error) {
    console.error('纹理加载失败:', error);
    
    // 使用默认纹理
    const fallbackTexture = await assetManager.loadTexture('textures/default.png');
}
```

### 3. 生命周期管理

```javascript
class GameScene {
    async initialize() {
        // 预加载关键资源
        await this.preloadCriticalAssets();
        
        // 启动后台预加载
        this.startBackgroundPreloading();
    }
    
    async preloadCriticalAssets() {
        const criticalAssets = [
            { url: 'models/player.glb', type: ASSET_TYPES.MODEL, priority: ASSET_PRIORITY.CRITICAL },
            { url: 'textures/ui.png', type: ASSET_TYPES.TEXTURE, priority: ASSET_PRIORITY.HIGH }
        ];
        
        await assetManager.preloadAssets(criticalAssets);
    }
    
    startBackgroundPreloading() {
        // 在空闲时预加载其他资源
        const backgroundAssets = [
            { url: 'models/environment.glb', type: ASSET_TYPES.MODEL },
            { url: 'sounds/music.mp3', type: ASSET_TYPES.SOUND }
        ];
        
        setTimeout(() => {
            assetManager.preloadAssets(backgroundAssets);
        }, 1000);
    }
    
    dispose() {
        // 清理场景资源
        assetManager.cleanup(0); // 立即清理所有资源
    }
}
```

## 配置选项

### AssetManager 配置

```javascript
const assetManagerOptions = {
    // 缓存配置
    maxCacheSize: 500 * 1024 * 1024,  // 最大缓存大小 (字节)
    enableAutoCleanup: true,          // 启用自动清理
    cleanupInterval: 60000,           // 清理间隔 (毫秒)
    
    // 加载配置
    maxConcurrentLoads: 6,            // 最大并发加载数
    retryAttempts: 3,                 // 重试次数
    retryDelay: 1000,                 // 重试延迟 (毫秒)
    
    // 预加载配置
    enablePreloading: true,           // 启用预加载
    preloadBatchSize: 5,              // 预加载批次大小
    
    // 调试配置
    enableDebugLogging: false,        // 启用调试日志
    enablePerformanceMonitoring: true // 启用性能监控
};
```

### AssetSystem 配置

```javascript
const assetSystemOptions = {
    // 自动加载配置
    autoLoadOnAdd: true,              // 实体添加时自动加载
    autoLoadDelay: 100,               // 自动加载延迟 (毫秒)
    
    // 批处理配置
    enableBatchLoading: true,         // 启用批处理加载
    batchSize: 5,                     // 批次大小
    batchInterval: 200,               // 批处理间隔 (毫秒)
    
    // 性能配置
    maxConcurrentLoads: 3,            // 最大并发加载数
    enablePreloading: true,           // 启用预加载
    
    // 清理配置
    enableAutoCleanup: true,          // 启用自动清理
    cleanupInterval: 30000,           // 清理间隔 (毫秒)
    unusedThreshold: 60000            // 未使用阈值 (毫秒)
};
```

## 故障排除

### 常见问题

1. **内存使用过高**
   - 检查缓存配置，适当降低 `maxCacheSize`
   - 启用自动清理，调整 `cleanupInterval`
   - 手动调用 `cleanup()` 方法

2. **加载速度慢**
   - 增加 `maxConcurrentLoads` 值
   - 启用预加载功能
   - 检查网络连接和资源服务器性能

3. **资源加载失败**
   - 检查资源URL是否正确
   - 验证资源文件格式是否支持
   - 查看控制台错误信息

### 调试工具

```javascript
// 获取详细的调试信息
const debugInfo = {
    manager: assetManager.getPerformanceStats(),
    cache: assetManager.cache.getStats(),
    loader: assetManager.loader.getDebugInfo()
};

console.log('资源管理器调试信息:', debugInfo);

// 监听所有事件进行调试
assetManager.onAny((eventName, data) => {
    console.log(`AssetManager事件: ${eventName}`, data);
});
```

## 总结

资源管理系统为《山海经》MMORPG提供了强大而灵活的资源管理能力。通过合理使用该系统，可以显著提升游戏的加载性能和用户体验。建议开发者根据具体需求调整配置参数，并充分利用事件系统进行监控和调试。
